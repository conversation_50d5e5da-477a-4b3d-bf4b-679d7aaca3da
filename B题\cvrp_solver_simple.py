#!/usr/bin/env python
# coding: utf-8
"""
CVRP求解器 - 简化版，只求解厨余垃圾这一类型
重点解决编码问题并生成单个路径图
"""

import math
import time
import json
import sys
import numpy as np
import matplotlib.pyplot as plt
from ortools.constraint_solver import routing_enums_pb2
from ortools.constraint_solver import pywrapcp
import matplotlib.patches as mpatches

def main():
    """简化版主函数，只求解厨余垃圾路径"""
    # 重定向输出到文件，确保使用utf-8编码
    with open('result_simple.txt', 'w', encoding='utf-8') as output_file:
        try:
            # 准备数据
            print("正在从data_new.json文件读取数据...")
            output_file.write("正在从data_new.json文件读取数据...\n")
            
            with open('data_new.json', 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            print("JSON数据读取成功")
            output_file.write("JSON数据读取成功\n")
            
            # 处理厂坐标
            locations = []
            collection_points = json_data['collection_points']
            print(f"JSON中的收集点数量: {len(collection_points)}个")
            output_file.write(f"JSON中的收集点数量: {len(collection_points)}个\n")
            
            # 按id排序收集点
            sorted_points = sorted(collection_points, key=lambda p: p['id'])
            
            for point in sorted_points:
                locations.append((point['x'], point['y']))
            
            # 只读取厨余垃圾的数据
            waste_type = 'kitchen_waste'
            waste_name = '厨余垃圾'
            type_color = 'green'
            
            # 读取垃圾分布信息
            waste_distribution = json_data['waste_distribution']
            
            # 准备厨余垃圾的需求数据
            demands = []
            for point in sorted_points:
                point_id = point['id']
                # 找到该点的垃圾分布数据
                waste_data = next((item for item in waste_distribution if item['collection_point_id'] == point_id), None)
                if waste_data:
                    demands.append(waste_data[waste_type])
                else:
                    demands.append(0)  # 如果没有找到对应的分布数据，设为0
            
            # 提取车辆容量和容积限制
            vehicle = next((v for v in json_data['vehicles'] if v['waste_type'] == waste_type), None)
            if not vehicle:
                print(f"错误: 未找到{waste_name}对应的车辆信息")
                output_file.write(f"错误: 未找到{waste_name}对应的车辆信息\n")
                return
            
            vehicle_capacity = vehicle['capacity']  # 吨
            vehicle_volume = vehicle['volume']      # 立方米
            unit_cost = vehicle['unit_cost']       # 单位距离成本
            
            # 计算体积
            density = waste_distribution[0]['densities'][waste_type]  # 密度 吨/立方米
            volumes = [0]  # 处理厂体积为0
            
            for demand in demands[1:]:  # 跳过处理厂
                volume = demand / density  # 体积 = 重量/密度
                volumes.append(volume)
            
            # 计算总重量和理论最小车辆数
            total_demand = sum(demands)
            total_volume = sum(volumes)
            
            # 计算最小车辆数 (同时考虑重量和体积约束)
            weight_based = math.ceil(total_demand / vehicle_capacity)
            volume_based = math.ceil(total_volume / vehicle_volume)
            
            # 取重量和体积约束的较大值
            min_vehicles = max(weight_based, volume_based)
            
            # 设置足够的车辆数量，为理论最小值的2倍
            num_vehicles = min_vehicles * 2
            
            print(f"{waste_name}总量: {total_demand:.2f}吨, 体积: {total_volume:.2f}立方米, 理论最小车辆数: {min_vehicles}辆")
            output_file.write(f"{waste_name}总量: {total_demand:.2f}吨, 体积: {total_volume:.2f}立方米, 理论最小车辆数: {min_vehicles}辆\n")
            
            # 计算距离矩阵
            distances = {}
            for from_node in range(len(locations)):
                distances[from_node] = {}
                for to_node in range(len(locations)):
                    if from_node == to_node:
                        distances[from_node][to_node] = 0
                    else:
                        # 计算欧氏距离
                        distances[from_node][to_node] = (
                            math.sqrt(
                                (locations[from_node][0] - locations[to_node][0]) ** 2 +
                                (locations[from_node][1] - locations[to_node][1]) ** 2))
            
            # 求解CVRP
            print(f"\n====== 正在求解 {waste_name} 路径优化 ======")
            output_file.write(f"\n====== 正在求解 {waste_name} 路径优化 ======\n")
            
            # 创建路由索引管理器
            manager = pywrapcp.RoutingIndexManager(len(locations), num_vehicles, 0)  # 0是处理厂
            
            # 创建路由模型
            routing = pywrapcp.RoutingModel(manager)
            
            # 注册距离回调函数
            def distance_callback(from_index, to_index):
                from_node = manager.IndexToNode(from_index)
                to_node = manager.IndexToNode(to_index)
                return int(distances[from_node][to_node] * 1000)  # 转换为整数
            
            transit_callback_index = routing.RegisterTransitCallback(distance_callback)
            
            # 定义距离成本
            routing.SetArcCostEvaluatorOfAllVehicles(transit_callback_index)
            
            # 添加重量容量约束
            def demand_callback(from_index):
                from_node = manager.IndexToNode(from_index)
                return int(demands[from_node] * 1000)  # 转为整数
            
            demand_callback_index = routing.RegisterUnaryTransitCallback(demand_callback)
            routing.AddDimensionWithVehicleCapacity(
                demand_callback_index,
                0,  # null capacity slack
                [int(vehicle_capacity * 1000)] * num_vehicles,  # 车辆重量容量
                True,  # 从起始位置开始计算
                'Weight')
            
            # 添加体积容量约束
            def volume_callback(from_index):
                from_node = manager.IndexToNode(from_index)
                return int(volumes[from_node] * 1000)  # 转为整数
            
            volume_callback_index = routing.RegisterUnaryTransitCallback(volume_callback)
            routing.AddDimensionWithVehicleCapacity(
                volume_callback_index,
                0,  # null capacity slack
                [int(vehicle_volume * 1000)] * num_vehicles,  # 车辆体积容量
                True,  # 从起始位置开始计算
                'Volume')
            
            # 设置求解策略
            search_parameters = pywrapcp.DefaultRoutingSearchParameters()
            search_parameters.first_solution_strategy = (
                routing_enums_pb2.FirstSolutionStrategy.PATH_CHEAPEST_ARC)
            search_parameters.local_search_metaheuristic = (
                routing_enums_pb2.LocalSearchMetaheuristic.GUIDED_LOCAL_SEARCH)
            search_parameters.time_limit.seconds = 30  # 求解时间限制
            
            # 求解问题
            start_time = time.time()
            solution = routing.SolveWithParameters(search_parameters)
            solve_time = time.time() - start_time
            
            if solution:
                print(f"求解成功! 用时: {solve_time:.2f}秒")
                output_file.write(f"求解成功! 用时: {solve_time:.2f}秒\n")
                
                # 输出结果
                print(f"\n====== {waste_name} 路径优化结果 ======")
                output_file.write(f"\n====== {waste_name} 路径优化结果 ======\n")
                print(f'目标值: {solution.ObjectiveValue() / 1000:.2f}')
                output_file.write(f'目标值: {solution.ObjectiveValue() / 1000:.2f}\n')
                
                total_distance = 0
                total_load = 0
                total_volume = 0
                total_cost = 0
                
                # 统计使用车辆数 (实际路径数)
                used_vehicles = 0
                
                # 收集路径数据用于绘图
                routes_for_plot = []
                loads = []
                volumes_used = []
                distances = []
                
                for vehicle_id in range(num_vehicles):
                    index = routing.Start(vehicle_id)
                    # 检查是否使用了该车辆
                    if solution.Value(routing.NextVar(index)) == routing.End(vehicle_id):
                        continue  # 该车辆未使用
                        
                    used_vehicles += 1
                    plan_output = f'路径 {used_vehicles}:'
                    route_distance = 0
                    route_load = 0
                    route_volume = 0
                    
                    route_for_plot = []
                    
                    while not routing.IsEnd(index):
                        node_index = manager.IndexToNode(index)
                        route_load += demands[node_index]
                        route_volume += volumes[node_index]
                        plan_output += f' {node_index} ->'
                        route_for_plot.append(node_index)
                        
                        previous_index = index
                        index = solution.Value(routing.NextVar(index))
                        route_distance += routing.GetArcCostForVehicle(
                            previous_index, index, vehicle_id) / 1000  # 转回实际距离
                            
                    node_index = manager.IndexToNode(index)
                    route_for_plot.append(node_index)  # 添加返回处理厂的路径
                    
                    route_cost = route_distance * unit_cost  # 路径成本 = 距离 * 单位成本
                    
                    plan_output += f' {node_index}'
                    plan_output += f' (距离: {route_distance:.2f}km, 重量: {route_load:.2f}吨, 体积: {route_volume:.2f}m³, 成本: {route_cost:.2f}元)'
                    print(plan_output)
                    output_file.write(plan_output + '\n')
                    
                    total_distance += route_distance
                    total_load += route_load
                    total_volume += route_volume
                    total_cost += route_cost
                    
                    # 保存绘图数据
                    routes_for_plot.append(route_for_plot)
                    loads.append(route_load)
                    volumes_used.append(route_volume)
                    distances.append(route_distance)
                
                # 计算各种评价指标
                # 1. 平均载重率
                avg_weight_ratio = (total_load / (used_vehicles * vehicle_capacity)) * 100 if used_vehicles > 0 else 0
                
                # 2. 平均体积利用率
                avg_volume_ratio = (total_volume / (used_vehicles * vehicle_volume)) * 100 if used_vehicles > 0 else 0
                
                # 3. 平均每车行驶距离
                avg_distance_per_vehicle = total_distance / used_vehicles if used_vehicles > 0 else 0
                
                # 4. 平均每车载重和体积
                avg_load_per_vehicle = total_load / used_vehicles if used_vehicles > 0 else 0
                avg_volume_per_vehicle = total_volume / used_vehicles if used_vehicles > 0 else 0
                
                # 5. 每吨垃圾的行驶距离和成本
                distance_per_ton = total_distance / total_load if total_load > 0 else 0
                cost_per_ton = total_cost / total_load if total_load > 0 else 0
                
                # 输出基本统计数据
                print('\n====== 路径优化结果统计 ======')
                output_file.write('\n====== 路径优化结果统计 ======\n')
                print(f'总距离: {total_distance:.2f} km')
                output_file.write(f'总距离: {total_distance:.2f} km\n')
                print(f'总载重: {total_load:.2f} 吨')
                output_file.write(f'总载重: {total_load:.2f} 吨\n')
                print(f'总体积: {total_volume:.2f} m³')
                output_file.write(f'总体积: {total_volume:.2f} m³\n')
                print(f'总成本: {total_cost:.2f} 元')
                output_file.write(f'总成本: {total_cost:.2f} 元\n')
                print(f'使用车辆数: {used_vehicles}')
                output_file.write(f'使用车辆数: {used_vehicles}\n')
                
                # 输出评价指标
                print('\n====== 评价指标 ======')
                output_file.write('\n====== 评价指标 ======\n')
                print(f'1. 平均载重率: {avg_weight_ratio:.2f}%')
                output_file.write(f'1. 平均载重率: {avg_weight_ratio:.2f}%\n')
                print(f'2. 平均体积利用率: {avg_volume_ratio:.2f}%')
                output_file.write(f'2. 平均体积利用率: {avg_volume_ratio:.2f}%\n')
                print(f'3. 平均每车行驶距离: {avg_distance_per_vehicle:.2f} km')
                output_file.write(f'3. 平均每车行驶距离: {avg_distance_per_vehicle:.2f} km\n')
                print(f'4. 平均每车载重: {avg_load_per_vehicle:.2f} 吨')
                output_file.write(f'4. 平均每车载重: {avg_load_per_vehicle:.2f} 吨\n')
                print(f'5. 平均每车体积: {avg_volume_per_vehicle:.2f} m³')
                output_file.write(f'5. 平均每车体积: {avg_volume_per_vehicle:.2f} m³\n')
                print(f'6. 每吨垃圾的行驶距离: {distance_per_ton:.2f} km/吨')
                output_file.write(f'6. 每吨垃圾的行驶距离: {distance_per_ton:.2f} km/吨\n')
                print(f'7. 每吨垃圾的运输成本: {cost_per_ton:.2f} 元/吨')
                output_file.write(f'7. 每吨垃圾的运输成本: {cost_per_ton:.2f} 元/吨\n')
                
                # 输出载重率和体积率
                print("\n各车辆资源利用率:")
                output_file.write("\n各车辆资源利用率:\n")
                for i in range(len(routes_for_plot)):
                    load_ratio = (loads[i] / vehicle_capacity) * 100
                    vol_ratio = (volumes_used[i] / vehicle_volume) * 100
                    print(f'车辆 {i+1}: 载重率={load_ratio:.2f}% ({loads[i]:.2f}/{vehicle_capacity:.2f}吨), '
                         f'体积率={vol_ratio:.2f}% ({volumes_used[i]:.2f}/{vehicle_volume:.2f}m³)')
                    output_file.write(f'车辆 {i+1}: 载重率={load_ratio:.2f}% ({loads[i]:.2f}/{vehicle_capacity:.2f}吨), '
                                     f'体积率={vol_ratio:.2f}% ({volumes_used[i]:.2f}/{vehicle_volume:.2f}m³)\n')
                
                # 绘制路径图
                plt.figure(figsize=(12, 10))
                
                # 设置中文字体支持
                try:
                    # 尝试设置中文字体
                    import matplotlib.font_manager as fm
                    # 检查系统中文字体
                    chinese_fonts = [f.name for f in fm.fontManager.ttflist if 'simhei' in f.name.lower() or 'microsoft yahei' in f.name.lower() or 'simsun' in f.name.lower() or 'fangsong' in f.name.lower() or 'kaiti' in f.name.lower()]
                    if chinese_fonts:
                        plt.rcParams['font.family'] = chinese_fonts[0]
                    else:
                        print("未找到支持中文的字体，将使用英文标题")
                        output_file.write("未找到支持中文的字体，将使用英文标题\n")
                except:
                    print("设置中文字体失败，将使用英文标题")
                    output_file.write("设置中文字体失败，将使用英文标题\n")
                
                # 绘制所有点
                depot_x, depot_y = locations[0]
                
                # 绘制收集点
                plt.scatter([loc[0] for loc in locations[1:]], 
                           [loc[1] for loc in locations[1:]],
                           c='black', s=50, label='收集点')
                
                # 突出显示处理厂
                plt.scatter(depot_x, depot_y, c='red', s=200, marker='*', label='处理厂 (0)')
                
                # 为每个点添加编号
                for i, loc in enumerate(locations):
                    plt.annotate(str(i), (loc[0], loc[1]), xytext=(5, 5), 
                                textcoords='offset points', fontsize=10)
                
                # 使用不同线型区分同类型的不同路径
                linestyles = ['-', '--', '-.', ':']
                
                # 绘制该类型的路径
                for i, route in enumerate(routes_for_plot):
                    route_x = [locations[j][0] for j in route]
                    route_y = [locations[j][1] for j in route]
                    
                    linestyle = linestyles[i % len(linestyles)]
                    plt.plot(route_x, route_y, c=type_color, linewidth=2.5, linestyle=linestyle, 
                            label=f'路径 {i+1}')
                
                # 添加标题和标签
                plt.title(f'{waste_name}路径优化方案', fontsize=16)
                plt.xlabel('X坐标 (km)', fontsize=12)
                plt.ylabel('Y坐标 (km)', fontsize=12)
                plt.grid(True, linestyle='--', alpha=0.7)
                
                # 添加图例 - 限制图例项数量
                if len(routes_for_plot) > 6:
                    # 如果路径太多，使用两行显示图例
                    plt.legend(loc='upper center', bbox_to_anchor=(0.5, -0.12),
                              fancybox=True, shadow=True, ncol=min(6, len(routes_for_plot)))
                else:
                    plt.legend(loc='best')
                
                # 保存图片
                filename_base = f'cvrp_routes_kitchen_waste_simple'
                plt.savefig(f'{filename_base}.png', dpi=300, bbox_inches='tight')
                plt.savefig(f'{filename_base}.pdf', bbox_inches='tight')
                plt.close()
                
                print(f"{waste_name}路径图已保存为 '{filename_base}.png' 和 '{filename_base}.pdf'")
                output_file.write(f"{waste_name}路径图已保存为 '{filename_base}.png' 和 '{filename_base}.pdf'\n")
                
                # 保存简要结果到单独文件
                with open('summary_simple.txt', 'w', encoding='utf-8') as f:
                    f.write(f"CVRP问题2求解摘要 - {waste_name}路径优化\n")
                    f.write(f"===========================================\n")
                    f.write(f"总行驶距离: {total_distance:.2f} km\n")
                    f.write(f"总运输成本: {total_cost:.2f} 元\n")
                    f.write(f"总使用车辆: {used_vehicles} 辆\n\n")
                    
                    f.write(f"{waste_name}:\n")
                    f.write(f"  总量: {total_load:.2f} 吨\n")
                    f.write(f"  总体积: {total_volume:.2f} m³\n")
                    f.write(f"  距离: {total_distance:.2f} km\n")
                    f.write(f"  成本: {total_cost:.2f} 元\n")
                    f.write(f"  车辆: {used_vehicles} 辆\n")
                    f.write(f"  载重限制: {vehicle_capacity} 吨\n")
                    f.write(f"  体积限制: {vehicle_volume} m³\n")
                    f.write(f"  单位成本: {unit_cost} 元/km\n")
                    f.write(f"  载重率: {avg_weight_ratio:.2f}%\n")
                    f.write(f"  体积率: {avg_volume_ratio:.2f}%\n\n")
                
                print("摘要结果已保存到summary_simple.txt")
                output_file.write("摘要结果已保存到summary_simple.txt\n")
            
            else:
                print(f"没有找到解! 用时: {solve_time:.2f}秒")
                output_file.write(f"没有找到解! 用时: {solve_time:.2f}秒\n")
        
        except Exception as e:
            print(f"程序执行出错: {e}")
            output_file.write(f"程序执行出错: {e}\n")
            import traceback
            traceback.print_exc(file=output_file)
    
    print("结果已保存到result_simple.txt文件中")

if __name__ == '__main__':
    main() 