正在从data_new.json文件读取数据...
JSON数据读取成功
JSON中的收集点数量: 31个
厨余垃圾总量: 39.39吨, 体积: 98.47立方米, 理论最小车辆数: 5辆

====== 正在求解 厨余垃圾 路径优化 ======
求解成功! 用时: 30.00秒

====== 厨余垃圾 路径优化结果 ======
目标值: 414.35
程序执行出错: <built-in function RoutingModel_GetArcCostForVehicle> returned a result with an error set
Traceback (most recent call last):
  File "D:\Users\Yan_DL\Desktop\2025年电工杯竞赛赛题\B题\cvrp_solver_simple.py", line 128, in distance_callback
    return int(distances[from_node][to_node] * 1000)  # 转换为整数
IndexError: list index out of range

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Users\Yan_DL\Desktop\2025年电工杯竞赛赛题\B题\cvrp_solver_simple.py", line 221, in main
    route_distance += routing.GetArcCostForVehicle(
  File "C:\Users\<USER>\anaconda3\lib\site-packages\ortools\constraint_solver\pywrapcp.py", line 5989, in GetArcCostForVehicle
    return _pywrapcp.RoutingModel_GetArcCostForVehicle(self, from_index, to_index, vehicle)
SystemError: <built-in function RoutingModel_GetArcCostForVehicle> returned a result with an error set
