#!/usr/bin/env python
# coding: utf-8
"""
数据处理模块 - 为中转站优化问题准备数据
从data_new.json中提取必要的数据
"""

import json
import numpy as np
import math
import os
import sys

def load_data():
    """加载基础数据"""
    print("正在从data_new.json文件读取数据...")
    
    # 获取当前文件所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # 获取父目录路径
    parent_dir = os.path.abspath(os.path.join(current_dir, os.pardir))
    # 构建data_new.json的完整路径
    data_file_path = os.path.join(parent_dir, 'data_new.json')
    
    try:
        with open(data_file_path, 'r', encoding='utf-8') as f:
            json_data = json.load(f)
        print("JSON数据读取成功")
    except Exception as e:
        print(f"读取JSON数据失败: {e}")
        print(f"尝试读取的文件路径: {data_file_path}")
        sys.exit(1)
    
    # 提取收集点数据
    collection_points = json_data['collection_points']
    # 按id排序收集点
    sorted_points = sorted(collection_points, key=lambda p: p['id'])
    
    # 提取中转站数据
    transfer_stations = json_data['transfer_stations']
    
    # 提取垃圾类型数据
    vehicles = json_data['vehicles']
    waste_types = ['kitchen_waste', 'recyclable_waste', 'hazardous_waste', 'other_waste']
    waste_names = ['厨余垃圾', '可回收物', '有害垃圾', '其他垃圾']
    
    # 提取垃圾分布数据
    waste_distribution = json_data['waste_distribution']
    
    return {
        'collection_points': sorted_points,
        'transfer_stations': transfer_stations,
        'vehicles': vehicles,
        'waste_types': waste_types,
        'waste_names': waste_names,
        'waste_distribution': waste_distribution
    }

def prepare_optimization_data(raw_data):
    """准备优化模型所需的数据结构"""
    collection_points = raw_data['collection_points']
    transfer_stations = raw_data['transfer_stations']
    vehicles = raw_data['vehicles']
    waste_types = raw_data['waste_types']
    waste_distribution = raw_data['waste_distribution']
    
    # 创建网点坐标集
    locations = []
    depot_loc = (0, 0)  # 处理厂坐标
    
    # 收集点坐标（id:1~30）
    collection_locs = []
    for point in collection_points:
        if point['id'] == 0:  # 处理厂
            depot_loc = (point['x'], point['y'])
        else:
            collection_locs.append((point['x'], point['y']))
    
    # 候选中转站坐标（id:31~35）
    transfer_locs = [(station['x'], station['y']) for station in transfer_stations]
    
    # 合并所有坐标（处理厂:0, 收集点:1~30, 中转站:31~35）
    locations = [depot_loc] + collection_locs + transfer_locs
    
    # 计算距离矩阵
    n_locations = len(locations)
    distance_matrix = np.zeros((n_locations, n_locations))
    
    for i in range(n_locations):
        for j in range(n_locations):
            if i == j:
                distance_matrix[i][j] = 0
            else:
                # 计算欧氏距离
                distance_matrix[i][j] = math.sqrt(
                    (locations[i][0] - locations[j][0]) ** 2 +
                    (locations[i][1] - locations[j][1]) ** 2)
    
    # 提取垃圾需求数据
    demands_by_type = {}
    
    for waste_type in waste_types:
        demands = []
        
        for point in collection_points:
            point_id = point['id']
            waste_data = next((item for item in waste_distribution 
                              if item['collection_point_id'] == point_id), None)
            if waste_data:
                demands.append(waste_data[waste_type])
            else:
                demands.append(0)  # 如果没有找到对应的分布数据，设为0
        
        demands_by_type[waste_type] = demands
    
    # 提取车辆容量、成本和碳排放参数
    vehicle_capacities = {}  # 吨
    vehicle_volumes = {}     # 立方米
    vehicle_costs = {}       # 单位距离成本
    carbon_emissions = {}    # 碳排放系数
    
    for vehicle in vehicles:
        vehicle_type = vehicle['waste_type']
        vehicle_capacities[vehicle_type] = vehicle['capacity']
        vehicle_volumes[vehicle_type] = vehicle['volume']
        vehicle_costs[vehicle_type] = vehicle['unit_cost']
        carbon_emissions[vehicle_type] = {
            'alpha': vehicle['carbon_emission1'],  # 基础排放系数 kg/km
            'beta': vehicle['carbon_emission2']    # 载重排放系数 kg/吨·km
        }
    
    # 提取中转站参数
    station_capacities = {}  # 各中转站对各类垃圾的存储上限
    station_time_windows = []  # 各中转站的时间窗口
    station_costs = []  # 各中转站的建设成本
    
    for station in transfer_stations:
        station_id = station['id']
        # 中转站对应的索引为 station_id - 31 + 31 = station_id
        station_capacities[station_id] = station['storage_capacities']
        station_time_windows.append(station['time_window'])
        station_costs.append(station['construction_cost'])
    
    # 提取密度数据用于体积计算
    densities = waste_distribution[0]['densities']
    
    # 计算每个车辆类型的最大路径时间（往返各一次，总时间不超过12小时）
    vehicle_speed = 40  # km/h
    max_route_time = 12  # 小时
    
    # 处理厂时间窗口
    depot_time_window = [6, 18]  # 6:00-18:00
    
    # 垃圾总量计算
    total_waste_by_type = {}
    for waste_type in waste_types:
        total_waste_by_type[waste_type] = sum(demands_by_type[waste_type])
    
    # 碳价格（假设值，用于碳排放成本计算）
    carbon_price = 100  # 元/吨CO2
    
    return {
        'locations': locations,
        'distance_matrix': distance_matrix,
        'depot_id': 0,
        'collection_ids': list(range(1, 31)),
        'transfer_ids': list(range(31, 36)),
        'demands_by_type': demands_by_type,
        'waste_types': waste_types,
        'waste_names': raw_data['waste_names'],
        'vehicle_capacities': vehicle_capacities,
        'vehicle_volumes': vehicle_volumes,
        'vehicle_costs': vehicle_costs,
        'vehicle_speed': vehicle_speed,
        'carbon_emissions': carbon_emissions,
        'carbon_price': carbon_price,
        'station_capacities': station_capacities,
        'station_time_windows': station_time_windows,
        'station_costs': station_costs,
        'depot_time_window': depot_time_window,
        'densities': densities,
        'total_waste_by_type': total_waste_by_type
    }

if __name__ == "__main__":
    # 测试数据加载和处理
    raw_data = load_data()
    opt_data = prepare_optimization_data(raw_data)
    
    print("\n数据处理结果摘要:")
    print(f"收集点数量: {len(opt_data['collection_ids'])}")
    print(f"候选中转站数量: {len(opt_data['transfer_ids'])}")
    print(f"垃圾类型数量: {len(opt_data['waste_types'])}")
    
    print("\n垃圾总量:")
    for waste_type, waste_name in zip(opt_data['waste_types'], opt_data['waste_names']):
        print(f"{waste_name}: {opt_data['total_waste_by_type'][waste_type]:.2f}吨")
    
    print("\n中转站建设成本:")
    for i, station_id in enumerate(opt_data['transfer_ids']):
        print(f"中转站{station_id}: {opt_data['station_costs'][i]/10000:.2f}万元") 