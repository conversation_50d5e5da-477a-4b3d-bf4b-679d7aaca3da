#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
传统机器学习模型
包含多种传统机器学习算法用于光伏发电功率预测
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, Ridge, Lasso, ElasticNet
from sklearn.svm import SVR
from sklearn.neighbors import KNeighborsRegressor
from sklearn.tree import DecisionTreeRegressor
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import xgboost as xgb
import joblib
import os

class TraditionalModels:
    """传统机器学习模型类"""
    
    def __init__(self):
        """初始化模型"""
        self.models = {}
        self.trained_models = {}
        self.model_scores = {}
        
        # 初始化所有模型
        self._initialize_models()
    
    def _initialize_models(self):
        """初始化所有传统机器学习模型"""
        self.models = {
            'Linear_Regression': LinearRegression(),
            
            'Ridge_Regression': Ridge(alpha=1.0),
            
            'Lasso_Regression': Lasso(alpha=0.1),
            
            'ElasticNet': ElasticNet(alpha=0.1, l1_ratio=0.5),
            
            'Random_Forest': RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            ),
            
            'Gradient_Boosting': GradientBoostingRegressor(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=6,
                random_state=42
            ),
            
            'XGBoost': xgb.XGBRegressor(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=6,
                random_state=42,
                n_jobs=-1
            ),
            
            'SVR': SVR(
                kernel='rbf',
                C=1.0,
                gamma='scale'
            ),
            
            'KNN': KNeighborsRegressor(
                n_neighbors=5,
                weights='distance'
            ),
            
            'Decision_Tree': DecisionTreeRegressor(
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42
            )
        }
    
    def train_single_model(self, model_name, X_train, y_train, X_val=None, y_val=None):
        """训练单个模型"""
        if model_name not in self.models:
            raise ValueError(f"模型 {model_name} 不存在")
        
        print(f"正在训练 {model_name}...")
        
        model = self.models[model_name]
        
        # 训练模型
        model.fit(X_train, y_train)
        
        # 保存训练好的模型
        self.trained_models[model_name] = model
        
        # 评估模型
        if X_val is not None and y_val is not None:
            y_pred = model.predict(X_val)
            
            # 计算评估指标
            mae = mean_absolute_error(y_val, y_pred)
            rmse = np.sqrt(mean_squared_error(y_val, y_pred))
            r2 = r2_score(y_val, y_pred)
            mape = np.mean(np.abs((y_val - y_pred) / (y_val + 1e-8))) * 100
            
            self.model_scores[model_name] = {
                'mae': mae,
                'rmse': rmse,
                'r2': r2,
                'mape': mape
            }
            
            print(f"{model_name} - MAE: {mae:.4f}, RMSE: {rmse:.4f}, R²: {r2:.4f}, MAPE: {mape:.2f}%")
        
        return model
    
    def train_all_models(self, X, y, test_size=0.2, random_state=42):
        """训练所有模型"""
        print("开始训练所有传统机器学习模型...")
        
        # 分割训练集和验证集
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=test_size, random_state=random_state, shuffle=False
        )
        
        print(f"训练集大小: {X_train.shape[0]}, 验证集大小: {X_val.shape[0]}")
        
        # 训练所有模型
        for model_name in self.models.keys():
            try:
                self.train_single_model(model_name, X_train, y_train, X_val, y_val)
            except Exception as e:
                print(f"训练 {model_name} 时出错: {str(e)}")
                continue
        
        # 保存模型
        self.save_models()
        
        return self.trained_models
    
    def hyperparameter_tuning(self, model_name, X, y, param_grid=None):
        """超参数调优"""
        if model_name not in self.models:
            raise ValueError(f"模型 {model_name} 不存在")
        
        print(f"正在对 {model_name} 进行超参数调优...")
        
        model = self.models[model_name]
        
        # 默认参数网格
        if param_grid is None:
            param_grid = self._get_default_param_grid(model_name)
        
        if param_grid:
            # 网格搜索
            grid_search = GridSearchCV(
                model, param_grid, 
                cv=5, 
                scoring='neg_mean_squared_error',
                n_jobs=-1,
                verbose=1
            )
            
            grid_search.fit(X, y)
            
            print(f"{model_name} 最佳参数: {grid_search.best_params_}")
            print(f"{model_name} 最佳得分: {-grid_search.best_score_:.4f}")
            
            # 更新模型
            self.models[model_name] = grid_search.best_estimator_
            
            return grid_search.best_estimator_
        else:
            print(f"{model_name} 没有定义参数网格，跳过调优")
            return model
    
    def _get_default_param_grid(self, model_name):
        """获取默认参数网格"""
        param_grids = {
            'Random_Forest': {
                'n_estimators': [50, 100, 200],
                'max_depth': [5, 10, 15],
                'min_samples_split': [2, 5, 10]
            },
            'XGBoost': {
                'n_estimators': [50, 100, 200],
                'learning_rate': [0.05, 0.1, 0.2],
                'max_depth': [3, 6, 9]
            },
            'SVR': {
                'C': [0.1, 1, 10],
                'gamma': ['scale', 'auto', 0.001, 0.01]
            },
            'Ridge_Regression': {
                'alpha': [0.1, 1.0, 10.0, 100.0]
            },
            'Lasso_Regression': {
                'alpha': [0.01, 0.1, 1.0, 10.0]
            }
        }
        
        return param_grids.get(model_name, None)
    
    def cross_validate_models(self, X, y, cv=5):
        """交叉验证所有模型"""
        print("进行交叉验证...")
        
        cv_scores = {}
        
        for model_name, model in self.models.items():
            try:
                scores = cross_val_score(
                    model, X, y, 
                    cv=cv, 
                    scoring='neg_mean_squared_error',
                    n_jobs=-1
                )
                
                cv_scores[model_name] = {
                    'mean_score': -scores.mean(),
                    'std_score': scores.std(),
                    'scores': -scores
                }
                
                print(f"{model_name} - CV RMSE: {-scores.mean():.4f} (+/- {scores.std() * 2:.4f})")
                
            except Exception as e:
                print(f"交叉验证 {model_name} 时出错: {str(e)}")
                continue
        
        return cv_scores
    
    def get_feature_importance(self, model_name, feature_names=None):
        """获取特征重要性"""
        if model_name not in self.trained_models:
            print(f"模型 {model_name} 尚未训练")
            return None
        
        model = self.trained_models[model_name]
        
        # 检查模型是否有特征重要性属性
        if hasattr(model, 'feature_importances_'):
            importance = model.feature_importances_
        elif hasattr(model, 'coef_'):
            importance = np.abs(model.coef_)
        else:
            print(f"模型 {model_name} 不支持特征重要性分析")
            return None
        
        if feature_names is not None:
            importance_df = pd.DataFrame({
                'feature': feature_names,
                'importance': importance
            }).sort_values('importance', ascending=False)
            
            return importance_df
        else:
            return importance
    
    def predict(self, model_name, X):
        """使用指定模型进行预测"""
        if model_name not in self.trained_models:
            raise ValueError(f"模型 {model_name} 尚未训练")
        
        model = self.trained_models[model_name]
        return model.predict(X)
    
    def ensemble_predict(self, X, method='average'):
        """集成预测"""
        if not self.trained_models:
            raise ValueError("没有训练好的模型")
        
        predictions = {}
        
        # 获取所有模型的预测
        for model_name, model in self.trained_models.items():
            try:
                pred = model.predict(X)
                predictions[model_name] = pred
            except Exception as e:
                print(f"模型 {model_name} 预测失败: {str(e)}")
                continue
        
        if not predictions:
            raise ValueError("所有模型预测都失败了")
        
        # 转换为DataFrame
        pred_df = pd.DataFrame(predictions)
        
        if method == 'average':
            # 简单平均
            ensemble_pred = pred_df.mean(axis=1)
        elif method == 'weighted':
            # 加权平均（基于模型性能）
            weights = []
            for model_name in pred_df.columns:
                if model_name in self.model_scores:
                    # 使用R²作为权重
                    weight = max(0, self.model_scores[model_name]['r2'])
                else:
                    weight = 1.0
                weights.append(weight)
            
            weights = np.array(weights)
            weights = weights / weights.sum()  # 归一化
            
            ensemble_pred = (pred_df * weights).sum(axis=1)
        else:
            raise ValueError(f"不支持的集成方法: {method}")
        
        return ensemble_pred.values
    
    def save_models(self, save_dir='results/models'):
        """保存所有训练好的模型"""
        os.makedirs(save_dir, exist_ok=True)
        
        for model_name, model in self.trained_models.items():
            model_file = os.path.join(save_dir, f'{model_name}.joblib')
            joblib.dump(model, model_file)
        
        print(f"模型已保存到 {save_dir}")
    
    def load_models(self, save_dir='results/models'):
        """加载保存的模型"""
        if not os.path.exists(save_dir):
            print(f"模型目录 {save_dir} 不存在")
            return
        
        for model_name in self.models.keys():
            model_file = os.path.join(save_dir, f'{model_name}.joblib')
            if os.path.exists(model_file):
                self.trained_models[model_name] = joblib.load(model_file)
                print(f"已加载模型: {model_name}")
        
        print(f"从 {save_dir} 加载了 {len(self.trained_models)} 个模型")
    
    def get_best_model(self):
        """获取最佳模型"""
        if not self.model_scores:
            print("没有模型评估分数")
            return None
        
        # 基于R²选择最佳模型
        best_model_name = max(self.model_scores.keys(), 
                             key=lambda x: self.model_scores[x]['r2'])
        
        return best_model_name, self.trained_models[best_model_name]

if __name__ == "__main__":
    # 测试传统模型
    from sklearn.datasets import make_regression
    
    # 生成测试数据
    X, y = make_regression(n_samples=1000, n_features=20, noise=0.1, random_state=42)
    
    # 创建模型实例
    traditional_models = TraditionalModels()
    
    # 训练所有模型
    trained_models = traditional_models.train_all_models(X, y)
    
    print(f"\n训练完成，共训练了 {len(trained_models)} 个模型")
    
    # 获取最佳模型
    best_model_name, best_model = traditional_models.get_best_model()
    print(f"最佳模型: {best_model_name}")
    
    # 集成预测测试
    ensemble_pred = traditional_models.ensemble_predict(X[:10])
    print(f"集成预测结果: {ensemble_pred[:5]}")
