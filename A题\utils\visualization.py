#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化工具模块
提供各种图表和可视化功能
"""

import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class Visualizer:
    """可视化工具类"""
    
    def __init__(self, save_dir='results/plots'):
        """初始化可视化工具"""
        self.save_dir = save_dir
        os.makedirs(save_dir, exist_ok=True)
        
    def plot_data_exploration(self, data):
        """数据探索可视化"""
        print("生成数据探索图表...")
        
        # 1. 发电功率时间序列图
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('光伏发电数据探索分析', fontsize=16, fontweight='bold')
        
        # 发电功率时间序列
        axes[0, 0].plot(data['datetime'], data['power_output'], alpha=0.7, linewidth=0.5)
        axes[0, 0].set_title('发电功率时间序列')
        axes[0, 0].set_xlabel('时间')
        axes[0, 0].set_ylabel('发电功率 (kW)')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 发电功率分布直方图
        axes[0, 1].hist(data['power_output'], bins=50, alpha=0.7, edgecolor='black')
        axes[0, 1].set_title('发电功率分布')
        axes[0, 1].set_xlabel('发电功率 (kW)')
        axes[0, 1].set_ylabel('频次')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 日内发电功率变化
        hourly_power = data.groupby('hour')['power_output'].mean()
        axes[1, 0].plot(hourly_power.index, hourly_power.values, marker='o', linewidth=2)
        axes[1, 0].set_title('日内平均发电功率变化')
        axes[1, 0].set_xlabel('小时')
        axes[1, 0].set_ylabel('平均发电功率 (kW)')
        axes[1, 0].grid(True, alpha=0.3)
        axes[1, 0].set_xticks(range(0, 24, 2))
        
        # 月度发电功率变化
        monthly_power = data.groupby('month')['power_output'].mean()
        axes[1, 1].plot(monthly_power.index, monthly_power.values, marker='s', linewidth=2)
        axes[1, 1].set_title('月度平均发电功率变化')
        axes[1, 1].set_xlabel('月份')
        axes[1, 1].set_ylabel('平均发电功率 (kW)')
        axes[1, 1].grid(True, alpha=0.3)
        axes[1, 1].set_xticks(range(1, 13))
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, 'data_exploration.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        # 2. 气象数据相关性分析
        self._plot_weather_correlation(data)
        
        # 3. 发电功率与气象因子关系
        self._plot_power_weather_relationship(data)
        
    def _plot_weather_correlation(self, data):
        """绘制气象数据相关性热力图"""
        weather_cols = ['solar_irradiance', 'temperature', 'humidity', 'wind_speed', 
                       'pressure', 'cloud_cover', 'precipitation', 'power_output']
        
        correlation_matrix = data[weather_cols].corr()
        
        plt.figure(figsize=(10, 8))
        sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0,
                   square=True, linewidths=0.5, cbar_kws={"shrink": .8})
        plt.title('气象数据与发电功率相关性分析', fontsize=14, fontweight='bold')
        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, 'weather_correlation.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
    def _plot_power_weather_relationship(self, data):
        """绘制发电功率与主要气象因子的关系"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('发电功率与气象因子关系分析', fontsize=16, fontweight='bold')
        
        # 发电功率 vs 太阳辐照度
        axes[0, 0].scatter(data['solar_irradiance'], data['power_output'], alpha=0.5, s=1)
        axes[0, 0].set_xlabel('太阳辐照度 (W/m²)')
        axes[0, 0].set_ylabel('发电功率 (kW)')
        axes[0, 0].set_title('发电功率 vs 太阳辐照度')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 发电功率 vs 温度
        axes[0, 1].scatter(data['temperature'], data['power_output'], alpha=0.5, s=1)
        axes[0, 1].set_xlabel('温度 (°C)')
        axes[0, 1].set_ylabel('发电功率 (kW)')
        axes[0, 1].set_title('发电功率 vs 温度')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 发电功率 vs 云量
        axes[1, 0].scatter(data['cloud_cover'], data['power_output'], alpha=0.5, s=1)
        axes[1, 0].set_xlabel('云量')
        axes[1, 0].set_ylabel('发电功率 (kW)')
        axes[1, 0].set_title('发电功率 vs 云量')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 发电功率 vs 湿度
        axes[1, 1].scatter(data['humidity'], data['power_output'], alpha=0.5, s=1)
        axes[1, 1].set_xlabel('湿度 (%)')
        axes[1, 1].set_ylabel('发电功率 (kW)')
        axes[1, 1].set_title('发电功率 vs 湿度')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, 'power_weather_relationship.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
    def plot_feature_importance(self, models_results):
        """绘制特征重要性图"""
        print("生成特征重要性图表...")
        
        # 获取有特征重要性的模型
        importance_models = ['Random_Forest', 'XGBoost', 'Gradient_Boosting']
        
        fig, axes = plt.subplots(1, len(importance_models), figsize=(18, 6))
        if len(importance_models) == 1:
            axes = [axes]
        
        for i, model_name in enumerate(importance_models):
            if model_name in models_results:
                model = models_results[model_name]
                
                if hasattr(model, 'feature_importances_'):
                    importance = model.feature_importances_
                    
                    # 获取前15个最重要的特征
                    top_indices = np.argsort(importance)[-15:]
                    top_importance = importance[top_indices]
                    
                    # 创建特征名称（简化）
                    feature_names = [f'Feature_{j}' for j in top_indices]
                    
                    axes[i].barh(range(len(top_importance)), top_importance)
                    axes[i].set_yticks(range(len(top_importance)))
                    axes[i].set_yticklabels(feature_names)
                    axes[i].set_xlabel('重要性')
                    axes[i].set_title(f'{model_name} 特征重要性')
                    axes[i].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, 'feature_importance.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
    def plot_model_comparison(self, evaluation_results):
        """绘制模型性能对比图"""
        print("生成模型性能对比图表...")
        
        if not evaluation_results:
            print("没有评估结果可供可视化")
            return
        
        # 准备数据
        models = list(evaluation_results.keys())
        mae_scores = [evaluation_results[model]['mae'] for model in models]
        rmse_scores = [evaluation_results[model]['rmse'] for model in models]
        r2_scores = [evaluation_results[model]['r2'] for model in models]
        mape_scores = [evaluation_results[model]['mape'] for model in models]
        
        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('模型性能对比分析', fontsize=16, fontweight='bold')
        
        # MAE对比
        axes[0, 0].bar(models, mae_scores, color='skyblue', alpha=0.7)
        axes[0, 0].set_title('平均绝对误差 (MAE)')
        axes[0, 0].set_ylabel('MAE')
        axes[0, 0].tick_params(axis='x', rotation=45)
        axes[0, 0].grid(True, alpha=0.3)
        
        # RMSE对比
        axes[0, 1].bar(models, rmse_scores, color='lightcoral', alpha=0.7)
        axes[0, 1].set_title('均方根误差 (RMSE)')
        axes[0, 1].set_ylabel('RMSE')
        axes[0, 1].tick_params(axis='x', rotation=45)
        axes[0, 1].grid(True, alpha=0.3)
        
        # R²对比
        axes[1, 0].bar(models, r2_scores, color='lightgreen', alpha=0.7)
        axes[1, 0].set_title('决定系数 (R²)')
        axes[1, 0].set_ylabel('R²')
        axes[1, 0].tick_params(axis='x', rotation=45)
        axes[1, 0].grid(True, alpha=0.3)
        
        # MAPE对比
        axes[1, 1].bar(models, mape_scores, color='gold', alpha=0.7)
        axes[1, 1].set_title('平均绝对百分比误差 (MAPE)')
        axes[1, 1].set_ylabel('MAPE (%)')
        axes[1, 1].tick_params(axis='x', rotation=45)
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, 'model_comparison.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        # 雷达图对比（标准化后）
        self._plot_radar_comparison(evaluation_results)
        
    def _plot_radar_comparison(self, evaluation_results):
        """绘制模型性能雷达图"""
        from math import pi
        
        # 选择前5个模型
        models = list(evaluation_results.keys())[:5]
        
        # 准备数据（标准化到0-1范围）
        metrics = ['MAE', 'RMSE', 'R²', 'MAPE']
        
        fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
        
        # 计算角度
        angles = [n / float(len(metrics)) * 2 * pi for n in range(len(metrics))]
        angles += angles[:1]  # 闭合图形
        
        colors = plt.cm.Set3(np.linspace(0, 1, len(models)))
        
        for i, model in enumerate(models):
            values = []
            
            # MAE和RMSE：越小越好，转换为1-normalized_value
            mae_norm = 1 - (evaluation_results[model]['mae'] / 
                           max([evaluation_results[m]['mae'] for m in models]))
            rmse_norm = 1 - (evaluation_results[model]['rmse'] / 
                            max([evaluation_results[m]['rmse'] for m in models]))
            
            # R²：越大越好，直接标准化
            r2_norm = evaluation_results[model]['r2'] / max([evaluation_results[m]['r2'] for m in models])
            
            # MAPE：越小越好，转换为1-normalized_value
            mape_norm = 1 - (evaluation_results[model]['mape'] / 
                            max([evaluation_results[m]['mape'] for m in models]))
            
            values = [mae_norm, rmse_norm, r2_norm, mape_norm]
            values += values[:1]  # 闭合图形
            
            ax.plot(angles, values, 'o-', linewidth=2, label=model, color=colors[i])
            ax.fill(angles, values, alpha=0.25, color=colors[i])
        
        # 添加标签
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(metrics)
        ax.set_ylim(0, 1)
        ax.set_title('模型性能雷达图对比', size=16, fontweight='bold', pad=20)
        ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        ax.grid(True)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, 'model_radar_comparison.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
    def plot_predictions(self, data, predictions):
        """绘制预测结果图"""
        print("生成预测结果图表...")
        
        # 1. 预测结果时间序列图
        fig, axes = plt.subplots(2, 1, figsize=(15, 10))
        fig.suptitle('光伏发电功率预测结果', fontsize=16, fontweight='bold')
        
        # 历史数据（最后7天）
        recent_data = data.tail(24 * 7)
        
        axes[0].plot(recent_data['datetime'], recent_data['power_output'], 
                    label='历史实际功率', linewidth=2, alpha=0.8)
        axes[0].plot(predictions['datetime'], predictions['predicted_power'], 
                    label='预测功率', linewidth=2, alpha=0.8, linestyle='--')
        axes[0].set_title('最近7天历史数据与未来24小时预测')
        axes[0].set_xlabel('时间')
        axes[0].set_ylabel('发电功率 (kW)')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # 预测结果详细图
        axes[1].plot(predictions['datetime'], predictions['predicted_power'], 
                    marker='o', linewidth=2, markersize=4)
        axes[1].set_title('未来24小时发电功率预测详情')
        axes[1].set_xlabel('时间')
        axes[1].set_ylabel('预测发电功率 (kW)')
        axes[1].grid(True, alpha=0.3)
        
        # 设置x轴时间格式
        for ax in axes:
            ax.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, 'prediction_results.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        # 2. 预测功率分布图
        self._plot_prediction_distribution(predictions)
        
    def _plot_prediction_distribution(self, predictions):
        """绘制预测功率分布图"""
        fig, axes = plt.subplots(1, 2, figsize=(12, 5))
        fig.suptitle('预测功率分析', fontsize=14, fontweight='bold')
        
        # 预测功率分布直方图
        axes[0].hist(predictions['predicted_power'], bins=15, alpha=0.7, 
                    edgecolor='black', color='skyblue')
        axes[0].set_title('预测功率分布')
        axes[0].set_xlabel('预测功率 (kW)')
        axes[0].set_ylabel('频次')
        axes[0].grid(True, alpha=0.3)
        
        # 按小时分组的预测功率
        predictions['hour'] = predictions['datetime'].dt.hour
        hourly_pred = predictions.groupby('hour')['predicted_power'].mean()
        
        axes[1].plot(hourly_pred.index, hourly_pred.values, marker='o', linewidth=2)
        axes[1].set_title('预测期间各小时平均功率')
        axes[1].set_xlabel('小时')
        axes[1].set_ylabel('平均预测功率 (kW)')
        axes[1].grid(True, alpha=0.3)
        axes[1].set_xticks(range(0, 24, 2))
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, 'prediction_distribution.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
    def plot_training_history(self, history_dict):
        """绘制深度学习模型训练历史"""
        if not history_dict:
            return
            
        print("生成训练历史图表...")
        
        n_models = len(history_dict)
        fig, axes = plt.subplots(n_models, 2, figsize=(12, 4 * n_models))
        
        if n_models == 1:
            axes = axes.reshape(1, -1)
        
        for i, (model_name, history) in enumerate(history_dict.items()):
            # 损失函数图
            axes[i, 0].plot(history['loss'], label='训练损失')
            if 'val_loss' in history:
                axes[i, 0].plot(history['val_loss'], label='验证损失')
            axes[i, 0].set_title(f'{model_name} - 损失函数')
            axes[i, 0].set_xlabel('Epoch')
            axes[i, 0].set_ylabel('Loss')
            axes[i, 0].legend()
            axes[i, 0].grid(True, alpha=0.3)
            
            # MAE图
            if 'mae' in history:
                axes[i, 1].plot(history['mae'], label='训练MAE')
                if 'val_mae' in history:
                    axes[i, 1].plot(history['val_mae'], label='验证MAE')
                axes[i, 1].set_title(f'{model_name} - MAE')
                axes[i, 1].set_xlabel('Epoch')
                axes[i, 1].set_ylabel('MAE')
                axes[i, 1].legend()
                axes[i, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, 'training_history.png'), dpi=300, bbox_inches='tight')
        plt.close()

if __name__ == "__main__":
    # 测试可视化工具
    from data_generator import SolarDataGenerator
    
    # 生成测试数据
    generator = SolarDataGenerator()
    test_data = generator.generate_solar_data(
        start_date='2023-01-01', 
        end_date='2023-01-07',
        freq='H'
    )
    
    # 创建可视化工具
    visualizer = Visualizer()
    
    # 生成数据探索图表
    visualizer.plot_data_exploration(test_data)
    
    print("可视化测试完成")
