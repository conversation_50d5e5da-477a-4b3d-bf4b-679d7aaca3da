# 城市垃圾分类运输中转站选址与路径优化 - ε-constraint方法

本项目实现了B题第三问的多目标优化模型，使用ε-constraint方法同时考虑成本最小化和碳排放最小化两个目标。

## ε-constraint方法简介

ε-constraint方法是多目标优化中常用的方法之一，其核心思想是：
1. 选择一个目标函数作为主要优化目标（例如：最小化成本）
2. 将其他目标函数（例如：碳排放）转化为约束条件，设定上限ε
3. 通过系统性地改变ε值，可以得到一系列Pareto最优解，绘制Pareto前沿曲线

数学表示：
```
min C_trans + C_build  s.t. E ≤ ε
```

其中：
- C_trans 是运输成本
- C_build 是中转站建设成本
- E 是碳排放量
- ε 是碳排放上限约束

## 项目结构

- `data_processor.py`: 数据处理模块，从data_new.json中提取必要数据
- `epsilon_constraint_solver.py`: ε-constraint优化求解器，包含核心算法实现（使用PuLP开源优化库）
- `epsilon_main.py`: 主程序，用于运行优化和可视化结果

## 算法实现

1. **极限值确定**：
   - 先求解无碳排放约束下的成本最小化问题，得到最大碳排放量E_worst
   - 使用二分查找法找到最小可行碳排放量E_best

2. **Pareto前沿生成**：
   - 在[E_best, E_worst]区间内选取6个均匀分布的ε值
   - 对每个ε值求解约束优化问题，得到相应的Pareto最优解

3. **结果可视化**：
   - 绘制Pareto前沿曲线
   - 为每个Pareto点生成详细的选址和路径方案图
   - 比较不同方案的中转站选择情况
   - 分析不同方案的成本构成

## 运行方法

1. 确保安装了必要的依赖：
   ```
   pip install numpy matplotlib pulp
   ```

2. 运行优化程序：
   ```
   cd transfer_opt
   python epsilon_main.py
   ```

3. 查看生成的结果（保存在results目录）：
   - `pareto_front.png`: Pareto前沿曲线图
   - `solution_X.png`: 每个方案的详细选址和分配图
   - `station_selection.png`: 不同方案的中转站选择比较
   - `cost_breakdown.png`: 不同方案的成本构成分析
   - `pareto_summary.txt`: 详细的数值结果和方案说明

## 输出结果说明

1. **Pareto前沿曲线**：
   - 横轴：碳排放量（吨CO2）
   - 纵轴：总成本（元）
   - 每个点代表一个Pareto最优解，标注了具体的成本和碳排放量

2. **方案详细图**：
   - 显示选择的中转站位置
   - 显示收集点到中转站的分配关系
   - 标注总成本和碳排放量

3. **中转站选择比较**：
   - 矩阵形式展示不同方案选择的中转站
   - 横轴：中转站编号
   - 纵轴：优化方案编号
   - 单元格中的"√"表示该方案选择了该中转站

4. **成本构成分析**：
   - 堆叠柱状图显示每个方案的成本构成
   - 区分运输成本和建设成本的比例
   - 标注具体的成本数值和百分比

## 决策建议

用户可以根据自身偏好选择适当的Pareto最优解：
- 如果成本更为重要，可以选择靠近Pareto前沿右侧的方案
- 如果碳排放更为重要，可以选择靠近Pareto前沿左侧的方案
- 可以根据中转站的地理位置和已有基础设施进一步优化选择

## 使用的开源库和技术

- **PuLP**: 用于线性规划优化的Python开源库
- **NumPy**: 用于数值计算
- **Matplotlib**: 用于数据可视化
- **CBC**: PuLP默认使用的开源线性规划求解器

## 注意事项

- 优化过程可能需要一定时间，特别是在寻找Pareto前沿的多个点时
- 可以通过调整epsilon_constraint_solver.py中的参数控制优化过程
- 如果在大型问题中遇到内存或性能问题，可以考虑减少Pareto点的数量 