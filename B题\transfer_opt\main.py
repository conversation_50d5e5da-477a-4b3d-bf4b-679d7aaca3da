#!/usr/bin/env python
# coding: utf-8
"""
城市垃圾分类运输的中转站选址与路径优化 - 问题3求解
整合第一阶段(中转站选址)和第二阶段(路径规划)的优化
"""

import time
import matplotlib.pyplot as plt
import numpy as np
from data_processor import load_data, prepare_optimization_data
from stage1_location import solve_location_allocation, plot_location_allocation
from stage2_routing import solve_all_routes, calculate_carbon_emissions, generate_summary

def plot_cost_breakdown(summary):
    """绘制成本构成饼图"""
    plt.figure(figsize=(10, 8))
    
    # 设置中文字体支持
    try:
        import matplotlib.font_manager as fm
        chinese_fonts = [f.name for f in fm.fontManager.ttflist if 'simhei' in f.name.lower() or 'microsoft yahei' in f.name.lower() or 'simsun' in f.name.lower() or 'fangsong' in f.name.lower() or 'kaiti' in f.name.lower()]
        if chinese_fonts:
            plt.rcParams['font.family'] = chinese_fonts[0]
    except:
        print("设置中文字体失败，将使用英文标题")
    
    # 提取成本数据
    transport_cost = summary['transport_cost']
    construction_cost = summary['construction_cost']
    carbon_cost = summary['carbon_cost']
    
    # 绘制饼图
    costs = [transport_cost, construction_cost, carbon_cost]
    labels = ['运输成本', '建设成本', '碳排放成本']
    
    plt.pie(costs, labels=labels, autopct='%1.1f%%', startangle=90, colors=['#66b3ff', '#99ff99', '#ffcc99'])
    plt.axis('equal')  # 保持饼图为圆形
    plt.title('总成本构成', fontsize=16)
    
    # 保存图片
    plt.savefig('transfer_opt/cost_breakdown.png', dpi=300, bbox_inches='tight')
    plt.savefig('transfer_opt/cost_breakdown.pdf', bbox_inches='tight')
    plt.close()
    
    print("成本构成饼图已保存")

def plot_waste_comparison(data, summary):
    """绘制各垃圾类型成本和排放对比图"""
    plt.figure(figsize=(12, 6))
    
    # 设置中文字体支持
    try:
        import matplotlib.font_manager as fm
        chinese_fonts = [f.name for f in fm.fontManager.ttflist if 'simhei' in f.name.lower() or 'microsoft yahei' in f.name.lower() or 'simsun' in f.name.lower() or 'fangsong' in f.name.lower() or 'kaiti' in f.name.lower()]
        if chinese_fonts:
            plt.rcParams['font.family'] = chinese_fonts[0]
    except:
        print("设置中文字体失败，将使用英文标题")
    
    # 提取数据
    waste_types = data['waste_types']
    waste_names = data['waste_names']
    
    # 提取各类型成本
    costs = [summary['transport_cost_by_type'][wt] for wt in waste_types]
    
    # 提取各类型碳排放（转换为千克方便显示）
    emissions = [summary['carbon_emissions_by_type'][wt] * 1000 for wt in waste_types]
    
    # 绘制柱状图
    x = np.arange(len(waste_names))
    width = 0.35
    
    fig, ax1 = plt.subplots(figsize=(12, 6))
    ax2 = ax1.twinx()
    
    bars1 = ax1.bar(x - width/2, costs, width, label='运输成本', color='#66b3ff')
    bars2 = ax2.bar(x + width/2, emissions, width, label='碳排放', color='#ffcc99')
    
    # 添加标签和标题
    ax1.set_xlabel('垃圾类型', fontsize=12)
    ax1.set_ylabel('运输成本（元）', fontsize=12, color='#66b3ff')
    ax2.set_ylabel('碳排放（千克CO2）', fontsize=12, color='#ffcc99')
    
    ax1.set_xticks(x)
    ax1.set_xticklabels(waste_names)
    
    # 添加图例
    ax1.legend(loc='upper left')
    ax2.legend(loc='upper right')
    
    plt.title('各垃圾类型运输成本与碳排放对比', fontsize=16)
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('transfer_opt/waste_comparison.png', dpi=300, bbox_inches='tight')
    plt.savefig('transfer_opt/waste_comparison.pdf', bbox_inches='tight')
    plt.close()
    
    print("垃圾类型对比图已保存")

def plot_integrated_solution(data, stage1_result, stage2_results):
    """绘制综合解决方案图"""
    plt.figure(figsize=(15, 12))
    
    # 设置中文字体支持
    try:
        import matplotlib.font_manager as fm
        chinese_fonts = [f.name for f in fm.fontManager.ttflist if 'simhei' in f.name.lower() or 'microsoft yahei' in f.name.lower() or 'simsun' in f.name.lower() or 'fangsong' in f.name.lower() or 'kaiti' in f.name.lower()]
        if chinese_fonts:
            plt.rcParams['font.family'] = chinese_fonts[0]
    except:
        print("设置中文字体失败，将使用英文标题")
    
    # 提取数据
    locations = data['locations']
    depot_id = data['depot_id']
    collection_ids = data['collection_ids']
    transfer_ids = data['transfer_ids']
    selected_stations = stage1_result['selected_stations']
    waste_types = data['waste_types']
    waste_names = data['waste_names']
    
    # 绘制depot(处理厂)
    plt.scatter(locations[depot_id][0], locations[depot_id][1], 
                c='red', s=200, marker='*', label='处理厂')
    
    # 绘制收集点
    plt.scatter([locations[i][0] for i in collection_ids], 
                [locations[i][1] for i in collection_ids],
                c='black', s=50, label='收集点')
    
    # 绘制候选中转站(区分选中和未选中)
    # 未选中的中转站
    unselected = [j for j in transfer_ids if j not in selected_stations]
    if unselected:
        plt.scatter([locations[j][0] for j in unselected], 
                    [locations[j][1] for j in unselected],
                    c='gray', s=100, marker='s', label='未选中转站')
    
    # 选中的中转站
    if selected_stations:
        plt.scatter([locations[j][0] for j in selected_stations], 
                    [locations[j][1] for j in selected_stations],
                    c='blue', s=150, marker='s', label='已选中转站')
    
    # 为每个点添加编号
    for i in range(len(locations)):
        plt.annotate(str(i), (locations[i][0], locations[i][1]), 
                     xytext=(5, 5), textcoords='offset points', fontsize=10)
    
    # 绘制所有路径，使用不同颜色区分不同垃圾类型
    colors = ['green', 'blue', 'purple', 'orange']
    linestyles = ['-', '--', '-.', ':']
    
    for type_idx, waste_type in enumerate(waste_types):
        stations_results = stage2_results[waste_type]
        type_color = colors[type_idx % len(colors)]
        
        for station_idx, (station_id, result) in enumerate(stations_results.items()):
            if not result:
                continue
                
            linestyle = linestyles[station_idx % len(linestyles)]
            route = result['route']
            
            # 绘制路径，添加小偏移避免路径重叠
            offset = 0.2 * (type_idx + 1) / len(waste_types)
            for i in range(len(route) - 1):
                # 计算偏移方向（垂直于路径）
                dx = locations[route[i+1]][0] - locations[route[i]][0]
                dy = locations[route[i+1]][1] - locations[route[i]][1]
                length = np.sqrt(dx**2 + dy**2)
                if length > 0:
                    # 垂直偏移
                    nx = -dy / length * offset
                    ny = dx / length * offset
                else:
                    nx, ny = 0, 0
                
                # 起点和终点加上偏移
                x1, y1 = locations[route[i]][0] + nx, locations[route[i]][1] + ny
                x2, y2 = locations[route[i+1]][0] + nx, locations[route[i+1]][1] + ny
                
                plt.plot([x1, x2], [y1, y2], 
                         c=type_color, linestyle=linestyle, linewidth=1.5, alpha=0.7)
    
    # 添加图例项
    legend_items = []
    for i, (waste_type, waste_name) in enumerate(zip(waste_types, waste_names)):
        legend_items.append(plt.Line2D([0], [0], color=colors[i], lw=2, label=waste_name))
    
    # 添加标题和标签
    plt.title('城市垃圾分类运输的中转站选址与路径优化方案', fontsize=16)
    plt.xlabel('X坐标 (km)', fontsize=12)
    plt.ylabel('Y坐标 (km)', fontsize=12)
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 创建自定义图例
    first_legend = plt.legend(handles=[
        plt.Line2D([0], [0], marker='*', color='w', markerfacecolor='red', markersize=10, label='处理厂'),
        plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='black', markersize=8, label='收集点'),
        plt.Line2D([0], [0], marker='s', color='w', markerfacecolor='blue', markersize=8, label='已选中转站'),
        plt.Line2D([0], [0], marker='s', color='w', markerfacecolor='gray', markersize=8, label='未选中转站')
    ], loc='upper left')
    
    plt.gca().add_artist(first_legend)
    plt.legend(handles=legend_items, loc='upper right')
    
    # 添加说明文本
    plt.figtext(0.5, 0.01, '注: 不同颜色代表不同垃圾类型，为清晰显示路径有轻微偏移', 
                ha='center', fontsize=10, bbox=dict(facecolor='white', alpha=0.8))
    
    # 保存图片
    plt.savefig('transfer_opt/integrated_solution.png', dpi=300, bbox_inches='tight')
    plt.savefig('transfer_opt/integrated_solution.pdf', bbox_inches='tight')
    plt.close()
    
    print("综合解决方案图已保存")

def main():
    """主函数"""
    print("开始求解城市垃圾分类运输的中转站选址与路径优化问题...")
    start_time = time.time()
    
    # 准备数据
    print("\n===== 第0阶段: 数据准备 =====")
    raw_data = load_data()
    opt_data = prepare_optimization_data(raw_data)
    
    # 第一阶段优化: 中转站选址与收集点分配
    print("\n===== 第1阶段: 中转站选址与收集点分配 =====")
    stage1_result = solve_location_allocation(opt_data)
    
    if not stage1_result:
        print("第一阶段优化失败，程序终止!")
        return
    
    # 绘制第一阶段结果
    plot_location_allocation(opt_data, stage1_result)
    
    # 第二阶段优化: 路径规划
    print("\n===== 第2阶段: 路径规划与时间窗优化 =====")
    stage2_results = solve_all_routes(opt_data, stage1_result)
    
    # 计算碳排放
    carbon_result = calculate_carbon_emissions(opt_data, stage2_results)
    
    # 生成摘要
    summary = generate_summary(opt_data, stage1_result, stage2_results, carbon_result)
    
    # 绘制综合结果图
    plot_cost_breakdown(summary)
    plot_waste_comparison(opt_data, summary)
    plot_integrated_solution(opt_data, stage1_result, stage2_results)
    
    # 计算总运行时间
    total_time = time.time() - start_time
    print(f"\n总运行时间: {total_time:.2f}秒")
    print("优化完成! 所有结果已保存到transfer_opt目录")

if __name__ == "__main__":
    main() 