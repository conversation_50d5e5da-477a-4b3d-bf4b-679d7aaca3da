#!/usr/bin/env python
# coding: utf-8
"""
城市垃圾分类运输的中转站选址与路径优化 - 主程序
使用ε-constraint方法求解多目标优化问题
"""

import time
import os
from epsilon_constraint_solver import (
    load_data, 
    prepare_optimization_data,
    generate_pareto_front,
    plot_pareto_front,
    plot_solution_details,
    plot_station_selection,
    plot_cost_breakdown,
    save_summary_table
)

def create_directory_structure():
    """创建必要的目录结构"""
    # 创建结果目录
    os.makedirs('results', exist_ok=True)
    print("已创建results目录用于存放优化结果")

def run_optimization():
    """运行ε-constraint优化"""
    start_time = time.time()
    
    print("=" * 80)
    print("城市垃圾分类运输中转站选址与路径优化 - ε-constraint方法")
    print("=" * 80)
    
    # 创建目录结构
    create_directory_structure()
    
    # 准备数据
    print("\n第1步: 数据准备...")
    raw_data = load_data()
    opt_data = prepare_optimization_data(raw_data)
    
    # 生成Pareto前沿
    print("\n第2步: 使用ε-constraint方法生成Pareto前沿...")
    pareto_points = generate_pareto_front(opt_data)
    
    if not pareto_points:
        print("优化失败，程序终止")
        return
    
    # 可视化和结果输出
    print("\n第3步: 生成结果分析和可视化...")
    
    # 绘制Pareto前沿曲线
    plot_pareto_front(pareto_points)
    
    # 为每个Pareto点绘制解决方案
    for i, point in enumerate(pareto_points):
        plot_solution_details(opt_data, point, i+1)
    
    # 比较不同方案的选址情况
    plot_station_selection(opt_data, pareto_points)
    
    # 比较不同方案的成本构成
    plot_cost_breakdown(pareto_points)
    
    # 保存结果摘要
    save_summary_table(pareto_points)
    
    # 计算总运行时间
    total_time = time.time() - start_time
    
    print("\n优化完成!")
    print(f"总运行时间: {total_time:.2f}秒")
    print(f"找到{len(pareto_points)}个Pareto最优解")
    print("所有结果已保存到results目录")
    print("=" * 80)

if __name__ == "__main__":
    run_optimization() 