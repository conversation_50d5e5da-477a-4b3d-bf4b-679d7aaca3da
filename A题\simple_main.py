#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
光伏电站发电功率日前预测问题 - 简化版主程序
2025年电工杯竞赛 A题

作者：AI Assistant
日期：2025年1月
"""

import os
import sys
import warnings
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt

# 设置中文字体和忽略警告
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
warnings.filterwarnings('ignore')

# 导入自定义模块
from data_generator import SolarDataGenerator
from feature_engineering import FeatureEngineer

# 尝试导入机器学习库
try:
    from sklearn.ensemble import RandomForestRegressor
    from sklearn.linear_model import LinearRegression
    from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
    from sklearn.model_selection import train_test_split
    SKLEARN_AVAILABLE = True
except ImportError:
    print("警告: scikit-learn 未安装，将使用简化的预测方法")
    SKLEARN_AVAILABLE = False

class SimpleSolarPredictor:
    """简化的光伏发电功率预测器"""
    
    def __init__(self):
        """初始化预测器"""
        self.data_generator = SolarDataGenerator()
        self.feature_engineer = FeatureEngineer()
        self.models = {}
        self.model_scores = {}
        
        # 创建必要的目录
        self.create_directories()
        
    def create_directories(self):
        """创建项目所需的目录结构"""
        directories = [
            'data',
            'results',
            'results/models',
            'results/plots',
            'results/predictions'
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            
    def load_or_generate_data(self):
        """加载或生成数据"""
        print("=" * 60)
        print("步骤1: 数据准备")
        print("=" * 60)
        
        data_file = 'data/solar_power_data.csv'
        
        if os.path.exists(data_file):
            print(f"发现已存在的数据文件: {data_file}")
            print("正在加载数据...")
            data = pd.read_csv(data_file, parse_dates=['datetime'])
        else:
            print("未发现数据文件，正在生成模拟数据...")
            data = self.data_generator.generate_solar_data()
            data.to_csv(data_file, index=False)
            print(f"数据已保存到: {data_file}")
            
        print(f"数据形状: {data.shape}")
        print(f"时间范围: {data['datetime'].min()} 到 {data['datetime'].max()}")
        print("\n数据预览:")
        print(data.head())
        
        return data
    
    def prepare_features(self, data):
        """特征工程"""
        print("\n" + "=" * 60)
        print("步骤2: 特征工程")
        print("=" * 60)
        
        print("正在进行特征工程...")
        features_data = self.feature_engineer.create_features(data)
        
        print(f"特征工程后数据形状: {features_data.shape}")
        print(f"特征列数: {len(features_data.columns)}")
        
        return features_data
    
    def simple_prediction_model(self, X, y):
        """简单的预测模型（不依赖scikit-learn）"""
        # 使用简单的线性回归
        # y = a * x1 + b * x2 + ... + c
        
        # 添加偏置项
        X_with_bias = np.column_stack([np.ones(X.shape[0]), X])
        
        # 使用正规方程求解: theta = (X^T * X)^(-1) * X^T * y
        try:
            XTX = np.dot(X_with_bias.T, X_with_bias)
            XTy = np.dot(X_with_bias.T, y)
            theta = np.linalg.solve(XTX, XTy)
            
            # 预测
            y_pred = np.dot(X_with_bias, theta)
            
            return theta, y_pred
        except np.linalg.LinAlgError:
            print("矩阵奇异，使用伪逆")
            theta = np.linalg.pinv(X_with_bias).dot(y)
            y_pred = np.dot(X_with_bias, theta)
            return theta, y_pred
    
    def train_models(self, features_data):
        """训练模型"""
        print("\n" + "=" * 60)
        print("步骤3: 模型训练")
        print("=" * 60)
        
        # 准备训练数据
        X, y = self.feature_engineer.prepare_training_data(features_data)
        
        print(f"训练数据形状: X={X.shape}, y={y.shape}")
        
        # 分割训练和测试数据
        if SKLEARN_AVAILABLE:
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, shuffle=False
            )
        else:
            # 手动分割
            split_idx = int(0.8 * len(X))
            X_train, X_test = X[:split_idx], X[split_idx:]
            y_train, y_test = y[:split_idx], y[split_idx:]
        
        print(f"训练集大小: {X_train.shape[0]}, 测试集大小: {X_test.shape[0]}")
        
        if SKLEARN_AVAILABLE:
            # 使用scikit-learn模型
            print("\n正在训练机器学习模型...")
            
            # 随机森林
            try:
                rf_model = RandomForestRegressor(n_estimators=50, random_state=42, n_jobs=-1)
                rf_model.fit(X_train, y_train)
                rf_pred = rf_model.predict(X_test)
                
                self.models['RandomForest'] = rf_model
                self.model_scores['RandomForest'] = self.calculate_metrics(y_test, rf_pred)
                print(f"随机森林 - R²: {self.model_scores['RandomForest']['r2']:.4f}")
            except Exception as e:
                print(f"随机森林训练失败: {e}")
            
            # 线性回归
            try:
                lr_model = LinearRegression()
                lr_model.fit(X_train, y_train)
                lr_pred = lr_model.predict(X_test)
                
                self.models['LinearRegression'] = lr_model
                self.model_scores['LinearRegression'] = self.calculate_metrics(y_test, lr_pred)
                print(f"线性回归 - R²: {self.model_scores['LinearRegression']['r2']:.4f}")
            except Exception as e:
                print(f"线性回归训练失败: {e}")
        else:
            # 使用简单模型
            print("\n正在训练简单预测模型...")
            try:
                theta, simple_pred = self.simple_prediction_model(X_train, y_train)
                
                # 在测试集上预测
                X_test_with_bias = np.column_stack([np.ones(X_test.shape[0]), X_test])
                test_pred = np.dot(X_test_with_bias, theta)
                
                self.models['SimpleLinear'] = theta
                self.model_scores['SimpleLinear'] = self.calculate_metrics(y_test, test_pred)
                print(f"简单线性模型 - R²: {self.model_scores['SimpleLinear']['r2']:.4f}")
            except Exception as e:
                print(f"简单模型训练失败: {e}")
        
        return X_test, y_test
    
    def calculate_metrics(self, y_true, y_pred):
        """计算评估指标"""
        if SKLEARN_AVAILABLE:
            mae = mean_absolute_error(y_true, y_pred)
            rmse = np.sqrt(mean_squared_error(y_true, y_pred))
            r2 = r2_score(y_true, y_pred)
        else:
            # 手动计算
            mae = np.mean(np.abs(y_true - y_pred))
            rmse = np.sqrt(np.mean((y_true - y_pred) ** 2))
            
            # R²计算
            ss_res = np.sum((y_true - y_pred) ** 2)
            ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
            r2 = 1 - (ss_res / ss_tot)
        
        mape = np.mean(np.abs((y_true - y_pred) / (y_true + 1e-8))) * 100
        
        return {
            'mae': mae,
            'rmse': rmse,
            'r2': r2,
            'mape': mape
        }
    
    def make_predictions(self, features_data):
        """进行日前预测"""
        print("\n" + "=" * 60)
        print("步骤4: 日前预测")
        print("=" * 60)
        
        # 选择最佳模型
        if self.model_scores:
            best_model_name = max(self.model_scores.keys(), 
                                 key=lambda x: self.model_scores[x]['r2'])
            print(f"使用最佳模型进行预测: {best_model_name}")
        else:
            print("没有训练好的模型，使用简单预测")
            return self.simple_forecast(features_data)
        
        # 生成未来24小时的预测
        predictions = self.feature_engineer.predict_next_24_hours(
            self.models[best_model_name], features_data
        )
        
        # 保存预测结果
        predictions_file = 'results/predictions/next_24h_predictions.csv'
        predictions.to_csv(predictions_file, index=False)
        print(f"预测结果已保存到: {predictions_file}")
        
        return predictions
    
    def simple_forecast(self, features_data):
        """简单的预测方法"""
        # 生成未来24小时的时间序列
        last_datetime = features_data['datetime'].max()
        future_times = pd.date_range(
            start=last_datetime + timedelta(hours=1),
            periods=24,
            freq='H'
        )
        
        predictions = []
        for future_time in future_times:
            hour = future_time.hour
            dayofyear = future_time.dayofyear
            
            # 简化的发电功率预测
            if 6 <= hour <= 18:  # 日照时间
                base_power = 500 * np.sin(np.pi * (hour - 6) / 12)
                seasonal_factor = 0.8 + 0.4 * np.sin(2 * np.pi * dayofyear / 365.25)
                pred_power = base_power * seasonal_factor
            else:
                pred_power = 0
            
            predictions.append({
                'datetime': future_time,
                'predicted_power': max(0, pred_power)
            })
        
        return pd.DataFrame(predictions)
    
    def generate_simple_plots(self, data, predictions):
        """生成简单的可视化图表"""
        print("\n" + "=" * 60)
        print("步骤5: 生成可视化图表")
        print("=" * 60)
        
        try:
            # 创建图表
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('光伏发电功率预测分析', fontsize=16, fontweight='bold')
            
            # 1. 发电功率时间序列
            recent_data = data.tail(24 * 7)  # 最近7天
            axes[0, 0].plot(recent_data['datetime'], recent_data['power_output'], 
                           label='历史功率', alpha=0.8)
            axes[0, 0].set_title('最近7天发电功率')
            axes[0, 0].set_ylabel('功率 (kW)')
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)
            
            # 2. 预测结果
            axes[0, 1].plot(predictions['datetime'], predictions['predicted_power'], 
                           marker='o', linewidth=2, markersize=4, color='red')
            axes[0, 1].set_title('未来24小时预测')
            axes[0, 1].set_ylabel('预测功率 (kW)')
            axes[0, 1].grid(True, alpha=0.3)
            
            # 3. 日内功率变化
            hourly_power = data.groupby('hour')['power_output'].mean()
            axes[1, 0].plot(hourly_power.index, hourly_power.values, marker='o')
            axes[1, 0].set_title('日内平均功率变化')
            axes[1, 0].set_xlabel('小时')
            axes[1, 0].set_ylabel('平均功率 (kW)')
            axes[1, 0].grid(True, alpha=0.3)
            
            # 4. 模型性能
            if self.model_scores:
                models = list(self.model_scores.keys())
                r2_scores = [self.model_scores[model]['r2'] for model in models]
                axes[1, 1].bar(models, r2_scores, alpha=0.7)
                axes[1, 1].set_title('模型性能对比 (R²)')
                axes[1, 1].set_ylabel('R² 分数')
                axes[1, 1].grid(True, alpha=0.3)
            else:
                axes[1, 1].text(0.5, 0.5, '无模型评估结果', 
                               ha='center', va='center', transform=axes[1, 1].transAxes)
                axes[1, 1].set_title('模型性能')
            
            plt.tight_layout()
            
            # 保存图表
            plot_file = 'results/plots/prediction_analysis.png'
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            print(f"图表已保存到: {plot_file}")
            plt.close()
            
        except Exception as e:
            print(f"生成图表时出错: {e}")
    
    def run(self):
        """运行完整的预测流程"""
        print("光伏电站发电功率日前预测系统 - 简化版")
        print("2025年电工杯竞赛 A题")
        print("开始时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        
        try:
            # 1. 数据准备
            data = self.load_or_generate_data()
            
            # 2. 特征工程
            features_data = self.prepare_features(data)
            
            # 3. 模型训练
            X_test, y_test = self.train_models(features_data)
            
            # 4. 日前预测
            predictions = self.make_predictions(features_data)
            
            # 5. 可视化
            self.generate_simple_plots(data, predictions)
            
            print("\n" + "=" * 60)
            print("预测任务完成!")
            print("=" * 60)
            print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("\n结果文件:")
            print("- 数据文件: data/solar_power_data.csv")
            print("- 预测结果: results/predictions/next_24h_predictions.csv")
            print("- 可视化图表: results/plots/prediction_analysis.png")
            
        except Exception as e:
            print(f"\n错误: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    # 创建预测器实例并运行
    predictor = SimpleSolarPredictor()
    predictor.run()
