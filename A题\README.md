# 光伏电站发电功率日前预测问题

## 项目简介

本项目解决"2025年电工杯竞赛"A题"光伏电站发电功率日前预测问题"。主要功能包括：

1. 自动生成和下载光伏发电相关数据集
2. 实现多种预测模型（传统机器学习 + 深度学习）
3. 进行日前24小时发电功率预测
4. 模型性能评估和可视化分析

## 文件说明

- `main.py` - 主程序入口
- `data_generator.py` - 数据生成和下载模块
- `feature_engineering.py` - 特征工程模块
- `models/` - 预测模型目录
  - `traditional_models.py` - 传统机器学习模型
  - `deep_learning_models.py` - 深度学习模型
- `utils/` - 工具函数目录
  - `visualization.py` - 可视化工具
  - `evaluation.py` - 模型评估工具
- `data/` - 数据存储目录
- `results/` - 结果输出目录

## 运行环境要求

- Python 3.8+
- 依赖包：
  - pandas
  - numpy
  - scikit-learn
  - tensorflow
  - matplotlib
  - seaborn
  - requests
  - xgboost

## 安装依赖

```bash
pip install pandas numpy scikit-learn tensorflow matplotlib seaborn requests xgboost
```

## 运行方法

直接运行主程序：

```bash
python main.py
```

## 功能特性

### 1. 数据生成
- 自动生成模拟的光伏电站历史发电数据
- 包含气象数据（太阳辐照度、温度、湿度、风速等）
- 考虑季节性、天气变化等因素

### 2. 特征工程
- 时间特征提取（小时、日、月、季节等）
- 气象特征处理和标准化
- 滞后特征和滑动窗口特征
- 天气分类特征

### 3. 预测模型
- **传统机器学习模型**：
  - Random Forest
  - XGBoost
  - Support Vector Regression
  - Linear Regression
- **深度学习模型**：
  - LSTM
  - CNN-LSTM
  - GRU
  - Transformer

### 4. 模型评估
- 多种评估指标（MAE、RMSE、MAPE等）
- 交叉验证
- 模型性能对比
- 预测结果可视化

### 5. 预测输出
- 日前24小时功率预测
- 置信区间估计
- 预测准确度分析
- 结果导出（CSV、图表）

## 技术方法

### 数据预处理
1. 数据清洗和异常值处理
2. 缺失值填充
3. 数据标准化和归一化
4. 时间序列数据重采样

### 特征工程
1. **时间特征**：小时、星期、月份、季节
2. **气象特征**：太阳辐照度、温度、湿度、风速、云量
3. **历史特征**：滞后1-24小时的功率值
4. **统计特征**：滑动平均、标准差等

### 模型设计
1. **数据驱动方法**：基于历史数据学习发电规律
2. **多模型融合**：结合多种算法的预测结果
3. **时序建模**：考虑时间依赖性的深度学习模型
4. **集成学习**：提高预测稳定性和准确性

## 预期效果

- 实现准确的日前24小时光伏发电功率预测
- 预测精度达到工程应用要求（MAPE < 15%）
- 提供多种模型对比和性能分析
- 生成详细的预测报告和可视化结果

## 创新点

1. **多模型融合**：结合传统机器学习和深度学习优势
2. **特征工程优化**：充分利用气象和时间特征
3. **实时预测能力**：支持在线预测和模型更新
4. **可视化分析**：提供丰富的图表和分析报告
