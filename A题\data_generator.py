#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
光伏发电数据生成器
生成模拟的光伏电站发电数据，包含气象数据和发电功率数据
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import requests
import json
import os

class SolarDataGenerator:
    """光伏发电数据生成器"""
    
    def __init__(self):
        """初始化数据生成器"""
        self.random_state = 42
        np.random.seed(self.random_state)
        
    def generate_solar_data(self, start_date='2022-01-01', end_date='2024-12-31', 
                           freq='H', station_capacity=1000):
        """
        生成光伏发电数据
        
        参数:
        - start_date: 开始日期
        - end_date: 结束日期  
        - freq: 数据频率 ('H'=小时, 'D'=天)
        - station_capacity: 电站装机容量(kW)
        
        返回:
        - DataFrame: 包含时间、气象数据和发电功率的数据框
        """
        print("正在生成光伏发电数据...")
        
        # 生成时间序列
        date_range = pd.date_range(start=start_date, end=end_date, freq=freq)
        
        # 初始化数据框
        data = pd.DataFrame({
            'datetime': date_range
        })
        
        # 添加时间特征
        data['year'] = data['datetime'].dt.year
        data['month'] = data['datetime'].dt.month
        data['day'] = data['datetime'].dt.day
        data['hour'] = data['datetime'].dt.hour
        data['dayofyear'] = data['datetime'].dt.dayofyear
        data['weekday'] = data['datetime'].dt.weekday
        
        # 生成气象数据
        data = self._generate_weather_data(data)
        
        # 生成发电功率数据
        data = self._generate_power_data(data, station_capacity)
        
        # 添加一些噪声和异常值
        data = self._add_noise_and_anomalies(data)
        
        print(f"生成了 {len(data)} 条数据记录")
        return data
    
    def _generate_weather_data(self, data):
        """生成气象数据"""
        n = len(data)
        
        # 太阳辐照度 (W/m²)
        # 基于时间的周期性变化
        hour_factor = np.sin(2 * np.pi * data['hour'] / 24)
        day_factor = np.sin(2 * np.pi * data['dayofyear'] / 365.25)
        
        # 基础辐照度（考虑季节和时间变化）
        base_irradiance = 800 * np.maximum(0, hour_factor) * (0.7 + 0.3 * day_factor)
        
        # 添加天气变化（云量影响）
        weather_noise = np.random.normal(0, 150, n)
        cloud_factor = np.random.beta(2, 5, n)  # 云量因子 0-1
        
        data['solar_irradiance'] = np.maximum(0, 
            base_irradiance * (1 - cloud_factor * 0.8) + weather_noise)
        
        # 环境温度 (°C)
        # 季节性温度变化
        seasonal_temp = 20 + 15 * np.sin(2 * np.pi * (data['dayofyear'] - 80) / 365.25)
        # 日内温度变化
        daily_temp = 8 * np.sin(2 * np.pi * (data['hour'] - 6) / 24)
        # 随机波动
        temp_noise = np.random.normal(0, 3, n)
        
        data['temperature'] = seasonal_temp + daily_temp + temp_noise
        
        # 相对湿度 (%)
        base_humidity = 60 + 20 * np.sin(2 * np.pi * data['dayofyear'] / 365.25)
        humidity_noise = np.random.normal(0, 10, n)
        data['humidity'] = np.clip(base_humidity + humidity_noise, 10, 95)
        
        # 风速 (m/s)
        base_wind = 3 + 2 * np.sin(2 * np.pi * data['dayofyear'] / 365.25)
        wind_noise = np.random.exponential(2, n)
        data['wind_speed'] = np.clip(base_wind + wind_noise, 0, 20)
        
        # 大气压力 (hPa)
        base_pressure = 1013 + 10 * np.sin(2 * np.pi * data['dayofyear'] / 365.25)
        pressure_noise = np.random.normal(0, 5, n)
        data['pressure'] = base_pressure + pressure_noise
        
        # 云量 (0-10)
        data['cloud_cover'] = np.clip(cloud_factor * 10 + np.random.normal(0, 1, n), 0, 10)
        
        # 降水量 (mm)
        rain_prob = 0.1 + 0.05 * np.sin(2 * np.pi * data['dayofyear'] / 365.25)
        rain_events = np.random.random(n) < rain_prob
        rain_amount = np.random.exponential(5, n) * rain_events
        data['precipitation'] = rain_amount
        
        return data
    
    def _generate_power_data(self, data, capacity):
        """生成发电功率数据"""
        # 基于太阳辐照度计算理论发电功率
        # 简化的光伏发电模型
        
        # 标准测试条件下的辐照度 (1000 W/m²)
        stc_irradiance = 1000
        
        # 基础发电功率（基于辐照度）
        power_ratio = data['solar_irradiance'] / stc_irradiance
        base_power = capacity * power_ratio
        
        # 温度系数影响（温度越高，效率越低）
        temp_coeff = -0.004  # 每度温度变化的功率系数
        temp_effect = 1 + temp_coeff * (data['temperature'] - 25)
        
        # 其他环境因素影响
        # 湿度影响
        humidity_effect = 1 - 0.001 * np.maximum(0, data['humidity'] - 60)
        
        # 风速影响（适度风速有利于散热）
        wind_effect = 1 + 0.01 * np.minimum(5, data['wind_speed'])
        
        # 云量影响（已在辐照度中考虑，这里添加额外的散射光影响）
        cloud_effect = 1 - 0.02 * data['cloud_cover']
        
        # 降水影响（雨天发电量显著下降）
        rain_effect = np.where(data['precipitation'] > 0.1, 0.3, 1.0)
        
        # 综合发电功率
        data['power_output'] = np.maximum(0, 
            base_power * temp_effect * humidity_effect * 
            wind_effect * cloud_effect * rain_effect)
        
        # 夜间发电量为0
        night_mask = (data['hour'] < 6) | (data['hour'] > 18)
        data.loc[night_mask, 'power_output'] = 0
        
        # 添加设备效率和老化因素
        # 设备效率（随机波动）
        efficiency_factor = np.random.normal(0.95, 0.02, len(data))
        efficiency_factor = np.clip(efficiency_factor, 0.8, 1.0)
        
        # 设备老化（随时间缓慢下降）
        aging_factor = 1 - 0.005 * (data['year'] - data['year'].min())
        
        data['power_output'] *= efficiency_factor * aging_factor
        
        return data
    
    def _add_noise_and_anomalies(self, data):
        """添加噪声和异常值"""
        n = len(data)
        
        # 添加测量噪声
        noise_level = 0.02  # 2%的噪声
        power_noise = np.random.normal(1, noise_level, n)
        data['power_output'] *= power_noise
        
        # 添加设备故障异常（随机将某些时段的发电量设为0）
        fault_prob = 0.001  # 0.1%的故障概率
        fault_events = np.random.random(n) < fault_prob
        data.loc[fault_events, 'power_output'] = 0
        
        # 添加维护停机（连续几小时发电量为0）
        maintenance_prob = 0.0001  # 维护概率
        for i in range(n - 24):
            if np.random.random() < maintenance_prob:
                # 维护持续4-8小时
                duration = np.random.randint(4, 9)
                end_idx = min(i + duration, n)
                data.loc[i:end_idx, 'power_output'] = 0
        
        # 确保功率不超过装机容量
        capacity = data['power_output'].max() * 1.1  # 估算装机容量
        data['power_output'] = np.clip(data['power_output'], 0, capacity)
        
        return data
    
    def download_real_weather_data(self, location='Beijing', api_key=None):
        """
        下载真实气象数据（可选功能）
        需要API密钥，这里提供框架代码
        """
        if api_key is None:
            print("未提供API密钥，使用模拟数据")
            return None
            
        try:
            # 这里可以集成真实的天气API
            # 例如：OpenWeatherMap, WeatherAPI等
            print("正在下载真实气象数据...")
            
            # 示例API调用（需要实际的API密钥）
            # url = f"http://api.openweathermap.org/data/2.5/weather?q={location}&appid={api_key}"
            # response = requests.get(url)
            # weather_data = response.json()
            
            print("真实气象数据下载功能需要API密钥")
            return None
            
        except Exception as e:
            print(f"下载气象数据失败: {e}")
            return None
    
    def save_data(self, data, filename='solar_power_data.csv'):
        """保存数据到文件"""
        filepath = os.path.join('data', filename)
        os.makedirs('data', exist_ok=True)
        data.to_csv(filepath, index=False)
        print(f"数据已保存到: {filepath}")
        
    def load_data(self, filename='solar_power_data.csv'):
        """从文件加载数据"""
        filepath = os.path.join('data', filename)
        if os.path.exists(filepath):
            data = pd.read_csv(filepath, parse_dates=['datetime'])
            print(f"数据已从 {filepath} 加载")
            return data
        else:
            print(f"文件 {filepath} 不存在")
            return None

if __name__ == "__main__":
    # 测试数据生成器
    generator = SolarDataGenerator()
    
    # 生成测试数据
    test_data = generator.generate_solar_data(
        start_date='2023-01-01', 
        end_date='2023-01-07',
        freq='H'
    )
    
    print("\n生成的测试数据:")
    print(test_data.head(10))
    print(f"\n数据统计信息:")
    print(test_data.describe())
