正在从data.json文件读取数据...
JSON数据读取成功
JSON中的收集点数量: 30个
数据读取完成! 共31个点, 包括处理厂
厨余垃圾总量: 7.84吨, 体积: 15.68立方米, 理论最小车辆数: 1辆
可回收物总量: 5.88吨, 体积: 58.80立方米, 理论最小车辆数: 3辆
有害垃圾总量: 1.96吨, 体积: 6.53立方米, 理论最小车辆数: 1辆
其他垃圾总量: 3.92吨, 体积: 9.80立方米, 理论最小车辆数: 1辆

====== 正在求解 厨余垃圾 路径优化 ======
求解成功! 用时: 54.39秒

====== 厨余垃圾 路径优化结果 ======
目标值: 0.00
路径 1: 0 -> 18 -> 7 -> 30 -> 15 -> 17 -> 16 -> 2 -> 21 -> 11 -> 5 -> 14 -> 1 -> 25 -> 13 -> 20 -> 6 -> 29 -> 8 -> 9 -> 12 -> 24 -> 4 -> 3 -> 23 -> 27 -> 19 -> 10 -> 22 -> 26 -> 28 -> 0 (距离: 0.00km, 重量: 7.84吨, 体积: 15.68m³, 成本: 0.00元)

====== 路径优化结果统计 ======
总距离: 0.00 km
总载重: 7.84 吨
总体积: 15.68 m³
总成本: 0.00 元
使用车辆数: 1

====== 评价指标 ======
1. 平均载重率: 98.00%
2. 平均体积利用率: 78.40%
3. 平均每车行驶距离: 0.00 km
4. 平均每车载重: 7.84 吨
5. 平均每车体积: 15.68 m³
6. 每吨垃圾的行驶距离: 0.00 km/吨
7. 每吨垃圾的运输成本: 0.00 元/吨

各车辆资源利用率:
车辆 1: 载重率=98.00% (7.84/8.00吨), 体积率=78.40% (15.68/20.00m³)

====== 正在求解 可回收物 路径优化 ======
求解成功! 用时: 60.00秒

====== 可回收物 路径优化结果 ======
目标值: 73.97
路径 1: 0 -> 22 -> 28 -> 26 -> 15 -> 30 -> 7 -> 18 -> 0 (距离: 13.61km, 重量: 1.04吨, 体积: 10.41m³, 成本: 27.22元)
路径 2: 0 -> 23 -> 4 -> 3 -> 1 -> 14 -> 5 -> 11 -> 21 -> 2 -> 16 -> 17 -> 0 (距离: 28.60km, 重量: 2.46吨, 体积: 24.57m³, 成本: 57.19元)
路径 3: 0 -> 19 -> 27 -> 20 -> 25 -> 13 -> 29 -> 6 -> 8 -> 9 -> 12 -> 24 -> 10 -> 0 (距离: 31.77km, 重量: 2.38吨, 体积: 23.82m³, 成本: 63.54元)

====== 路径优化结果统计 ======
总距离: 73.97 km
总载重: 5.88 吨
总体积: 58.80 m³
总成本: 147.95 元
使用车辆数: 3

====== 评价指标 ======
1. 平均载重率: 32.67%
2. 平均体积利用率: 78.40%
3. 平均每车行驶距离: 24.66 km
4. 平均每车载重: 1.96 吨
5. 平均每车体积: 19.60 m³
6. 每吨垃圾的行驶距离: 12.58 km/吨
7. 每吨垃圾的运输成本: 25.16 元/吨

各车辆资源利用率:
车辆 1: 载重率=17.35% (1.04/6.00吨), 体积率=41.64% (10.41/25.00m³)
车辆 2: 载重率=40.95% (2.46/6.00吨), 体积率=98.28% (24.57/25.00m³)
车辆 3: 载重率=39.70% (2.38/6.00吨), 体积率=95.28% (23.82/25.00m³)

====== 正在求解 有害垃圾 路径优化 ======
求解成功! 用时: 60.00秒

====== 有害垃圾 路径优化结果 ======
目标值: 53.12
路径 1: 0 -> 22 -> 10 -> 24 -> 12 -> 9 -> 8 -> 29 -> 6 -> 20 -> 13 -> 25 -> 1 -> 14 -> 5 -> 11 -> 21 -> 2 -> 16 -> 17 -> 3 -> 4 -> 23 -> 27 -> 19 -> 26 -> 15 -> 30 -> 7 -> 28 -> 18 -> 0 (距离: 53.12km, 重量: 1.96吨, 体积: 6.53m³, 成本: 265.61元)

====== 路径优化结果统计 ======
总距离: 53.12 km
总载重: 1.96 吨
总体积: 6.53 m³
总成本: 265.61 元
使用车辆数: 1

====== 评价指标 ======
1. 平均载重率: 65.33%
2. 平均体积利用率: 65.33%
3. 平均每车行驶距离: 53.12 km
4. 平均每车载重: 1.96 吨
5. 平均每车体积: 6.53 m³
6. 每吨垃圾的行驶距离: 27.10 km/吨
7. 每吨垃圾的运输成本: 135.51 元/吨

各车辆资源利用率:
车辆 1: 载重率=65.33% (1.96/3.00吨), 体积率=65.33% (6.53/10.00m³)

====== 正在求解 其他垃圾 路径优化 ======
求解成功! 用时: 60.00秒

====== 其他垃圾 路径优化结果 ======
目标值: 53.12
路径 1: 0 -> 22 -> 10 -> 24 -> 12 -> 9 -> 8 -> 29 -> 6 -> 20 -> 13 -> 25 -> 1 -> 14 -> 5 -> 11 -> 21 -> 2 -> 16 -> 17 -> 3 -> 4 -> 23 -> 27 -> 19 -> 26 -> 15 -> 30 -> 7 -> 28 -> 18 -> 0 (距离: 53.12km, 重量: 3.92吨, 体积: 9.80m³, 成本: 95.62元)

====== 路径优化结果统计 ======
总距离: 53.12 km
总载重: 3.92 吨
总体积: 9.80 m³
总成本: 95.62 元
使用车辆数: 1

====== 评价指标 ======
1. 平均载重率: 39.20%
2. 平均体积利用率: 54.44%
3. 平均每车行驶距离: 53.12 km
4. 平均每车载重: 3.92 吨
5. 平均每车体积: 9.80 m³
6. 每吨垃圾的行驶距离: 13.55 km/吨
7. 每吨垃圾的运输成本: 24.39 元/吨

各车辆资源利用率:
车辆 1: 载重率=39.20% (3.92/10.00吨), 体积率=54.44% (9.80/18.00m³)
厨余垃圾路径图已保存为 'cvrp_routes_kitchen_waste.png' 和 'cvrp_routes_kitchen_waste.pdf'
可回收物路径图已保存为 'cvrp_routes_recyclable_waste.png' 和 'cvrp_routes_recyclable_waste.pdf'
有害垃圾路径图已保存为 'cvrp_routes_hazardous_waste.png' 和 'cvrp_routes_hazardous_waste.pdf'
其他垃圾路径图已保存为 'cvrp_routes_other_waste.png' 和 'cvrp_routes_other_waste.pdf'
多类型路径图已保存为 'cvrp_multi_routes.png' 和 'cvrp_multi_routes.pdf'

====== 系统整体性能 ======
总行驶距离: 180.22 km
总运输成本: 509.17 元
总使用车辆: 6 辆

各垃圾类型统计:
厨余垃圾: 距离=0.00km, 成本=0.00元, 车辆=1辆, 载重率=98.00%, 体积率=78.40%
可回收物: 距离=73.97km, 成本=147.95元, 车辆=3辆, 载重率=32.67%, 体积率=78.40%
有害垃圾: 距离=53.12km, 成本=265.61元, 车辆=1辆, 载重率=65.33%, 体积率=65.33%
其他垃圾: 距离=53.12km, 成本=95.62元, 车辆=1辆, 载重率=39.20%, 体积率=54.44%
