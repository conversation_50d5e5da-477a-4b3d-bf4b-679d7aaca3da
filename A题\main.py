#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
光伏电站发电功率日前预测问题 - 主程序
2025年电工杯竞赛 A题

作者：AI Assistant
日期：2025年1月
"""

import os
import sys
import warnings
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体和忽略警告
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
warnings.filterwarnings('ignore')

# 导入自定义模块
from data_generator import SolarDataGenerator
from feature_engineering import FeatureEngineer
from models.traditional_models import TraditionalModels
from models.deep_learning_models import DeepLearningModels
from utils.visualization import Visualizer
from utils.evaluation import ModelEvaluator

class SolarPowerPredictor:
    """光伏发电功率预测主类"""
    
    def __init__(self):
        """初始化预测器"""
        self.data_generator = SolarDataGenerator()
        self.feature_engineer = FeatureEngineer()
        self.traditional_models = TraditionalModels()
        self.dl_models = DeepLearningModels()
        self.visualizer = Visualizer()
        self.evaluator = ModelEvaluator()
        
        # 创建必要的目录
        self.create_directories()
        
    def create_directories(self):
        """创建项目所需的目录结构"""
        directories = [
            'data',
            'results',
            'results/models',
            'results/plots',
            'results/predictions'
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            
    def load_or_generate_data(self):
        """加载或生成数据"""
        print("=" * 60)
        print("步骤1: 数据准备")
        print("=" * 60)
        
        data_file = 'data/solar_power_data.csv'
        
        if os.path.exists(data_file):
            print(f"发现已存在的数据文件: {data_file}")
            print("正在加载数据...")
            data = pd.read_csv(data_file, parse_dates=['datetime'])
        else:
            print("未发现数据文件，正在生成模拟数据...")
            data = self.data_generator.generate_solar_data()
            data.to_csv(data_file, index=False)
            print(f"数据已保存到: {data_file}")
            
        print(f"数据形状: {data.shape}")
        print(f"时间范围: {data['datetime'].min()} 到 {data['datetime'].max()}")
        print("\n数据预览:")
        print(data.head())
        
        return data
    
    def prepare_features(self, data):
        """特征工程"""
        print("\n" + "=" * 60)
        print("步骤2: 特征工程")
        print("=" * 60)
        
        print("正在进行特征工程...")
        features_data = self.feature_engineer.create_features(data)
        
        print(f"特征工程后数据形状: {features_data.shape}")
        print(f"特征列数: {len(features_data.columns)}")
        print("\n主要特征:")
        for i, col in enumerate(features_data.columns):
            if i < 10:  # 只显示前10个特征
                print(f"  {col}")
            elif i == 10:
                print(f"  ... 还有 {len(features_data.columns) - 10} 个特征")
                break
                
        return features_data
    
    def train_models(self, features_data):
        """训练所有模型"""
        print("\n" + "=" * 60)
        print("步骤3: 模型训练")
        print("=" * 60)
        
        # 准备训练数据
        X, y = self.feature_engineer.prepare_training_data(features_data)
        
        print(f"训练数据形状: X={X.shape}, y={y.shape}")
        
        # 训练传统机器学习模型
        print("\n正在训练传统机器学习模型...")
        traditional_results = self.traditional_models.train_all_models(X, y)
        
        # 训练深度学习模型
        print("\n正在训练深度学习模型...")
        dl_results = self.dl_models.train_all_models(X, y)
        
        # 合并结果
        all_results = {**traditional_results, **dl_results}
        
        return all_results, X, y
    
    def evaluate_models(self, models_results, X, y):
        """评估所有模型"""
        print("\n" + "=" * 60)
        print("步骤4: 模型评估")
        print("=" * 60)
        
        evaluation_results = self.evaluator.evaluate_all_models(models_results, X, y)
        
        # 显示评估结果
        print("\n模型性能对比:")
        print("-" * 80)
        print(f"{'模型名称':<20} {'MAE':<10} {'RMSE':<10} {'MAPE(%)':<10} {'R²':<10}")
        print("-" * 80)
        
        for model_name, metrics in evaluation_results.items():
            print(f"{model_name:<20} {metrics['mae']:<10.4f} {metrics['rmse']:<10.4f} "
                  f"{metrics['mape']:<10.2f} {metrics['r2']:<10.4f}")
        
        return evaluation_results
    
    def make_predictions(self, models_results, features_data):
        """进行日前预测"""
        print("\n" + "=" * 60)
        print("步骤5: 日前预测")
        print("=" * 60)
        
        # 选择最佳模型进行预测
        best_model_name = self.evaluator.get_best_model()
        best_model = models_results[best_model_name]
        
        print(f"使用最佳模型进行预测: {best_model_name}")
        
        # 生成未来24小时的预测
        predictions = self.feature_engineer.predict_next_24_hours(
            best_model, features_data
        )
        
        # 保存预测结果
        predictions_file = 'results/predictions/next_24h_predictions.csv'
        predictions.to_csv(predictions_file, index=False)
        print(f"预测结果已保存到: {predictions_file}")
        
        return predictions, best_model_name
    
    def generate_visualizations(self, data, features_data, models_results, 
                              evaluation_results, predictions):
        """生成可视化图表"""
        print("\n" + "=" * 60)
        print("步骤6: 生成可视化图表")
        print("=" * 60)
        
        # 数据探索可视化
        print("正在生成数据探索图表...")
        self.visualizer.plot_data_exploration(data)
        
        # 特征重要性可视化
        print("正在生成特征重要性图表...")
        self.visualizer.plot_feature_importance(models_results)
        
        # 模型性能对比
        print("正在生成模型性能对比图表...")
        self.visualizer.plot_model_comparison(evaluation_results)
        
        # 预测结果可视化
        print("正在生成预测结果图表...")
        self.visualizer.plot_predictions(data, predictions)
        
        print("所有图表已保存到 results/plots/ 目录")
    
    def generate_report(self, evaluation_results, predictions, best_model_name):
        """生成预测报告"""
        print("\n" + "=" * 60)
        print("步骤7: 生成预测报告")
        print("=" * 60)
        
        report_file = 'results/prediction_report.txt'
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("光伏电站发电功率日前预测报告\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("1. 模型性能总结\n")
            f.write("-" * 30 + "\n")
            for model_name, metrics in evaluation_results.items():
                f.write(f"{model_name}:\n")
                f.write(f"  MAE: {metrics['mae']:.4f}\n")
                f.write(f"  RMSE: {metrics['rmse']:.4f}\n")
                f.write(f"  MAPE: {metrics['mape']:.2f}%\n")
                f.write(f"  R²: {metrics['r2']:.4f}\n\n")
            
            f.write(f"2. 最佳模型: {best_model_name}\n\n")
            
            f.write("3. 日前24小时预测结果\n")
            f.write("-" * 30 + "\n")
            for _, row in predictions.iterrows():
                f.write(f"{row['datetime']}: {row['predicted_power']:.2f} kW\n")
        
        print(f"预测报告已保存到: {report_file}")
    
    def run(self):
        """运行完整的预测流程"""
        print("光伏电站发电功率日前预测系统")
        print("2025年电工杯竞赛 A题")
        print("开始时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        
        try:
            # 1. 数据准备
            data = self.load_or_generate_data()
            
            # 2. 特征工程
            features_data = self.prepare_features(data)
            
            # 3. 模型训练
            models_results, X, y = self.train_models(features_data)
            
            # 4. 模型评估
            evaluation_results = self.evaluate_models(models_results, X, y)
            
            # 5. 日前预测
            predictions, best_model_name = self.make_predictions(models_results, features_data)
            
            # 6. 可视化
            self.generate_visualizations(data, features_data, models_results, 
                                       evaluation_results, predictions)
            
            # 7. 生成报告
            self.generate_report(evaluation_results, predictions, best_model_name)
            
            print("\n" + "=" * 60)
            print("预测任务完成!")
            print("=" * 60)
            print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("\n结果文件:")
            print("- 数据文件: data/solar_power_data.csv")
            print("- 预测结果: results/predictions/next_24h_predictions.csv")
            print("- 可视化图表: results/plots/")
            print("- 预测报告: results/prediction_report.txt")
            
        except Exception as e:
            print(f"\n错误: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    # 创建预测器实例并运行
    predictor = SolarPowerPredictor()
    predictor.run()
