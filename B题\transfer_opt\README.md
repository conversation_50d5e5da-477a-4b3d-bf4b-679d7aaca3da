# 城市垃圾分类运输中转站选址与路径优化 - 问题3求解

本项目实现了B题第三问的优化模型，包含中转站选址、路径规划、时间窗口优化与碳排放计算。

## 项目结构

项目包含以下关键文件：

- `data_processor.py`: 数据处理模块，用于从data_new.json提取和准备优化所需的数据
- `stage1_location.py`: 第一阶段优化模型，实现中转站选址和收集点分配
- `stage2_routing.py`: 第二阶段优化模型，实现路径规划和时间窗口优化
- `main.py`: 主程序文件，整合两个阶段的优化过程并生成综合结果

## 实现的优化模型

本项目实现了一个两阶段优化模型：

1. **第一阶段（Benders分解）**：
   - 决定哪些候选中转站被选中（二元变量y_j）
   - 确定收集点到中转站的分配关系（二元变量x_ij）
   - 综合考虑运输成本和建设成本

2. **第二阶段（路径规划）**：
   - 对每个选定的中转站和垃圾类型，求解带时间窗的CVRP问题
   - 考虑处理厂和中转站的时间窗约束
   - 计算碳排放量和碳排放成本

## 运行方法

1. 确保安装了必要的依赖库：
   ```
   pip install numpy matplotlib gurobipy ortools
   ```

2. 运行主程序：
   ```
   python main.py
   ```

3. 查看生成的结果（保存在当前目录）：
   - `stage1_result.png`：中转站选址和分配方案图
   - `routes_*.png`：各类垃圾的路径规划图
   - `integrated_solution.png`：综合优化方案图
   - `cost_breakdown.png`：成本构成饼图
   - `waste_comparison.png`：各垃圾类型成本与碳排放对比图
   - `optimization_summary.txt`：优化结果详细报告

## 模型参数说明

- 垃圾类型：厨余垃圾、可回收物、有害垃圾、其他垃圾
- 中转站时间窗口：根据附件中的数据设置
- 处理厂时间窗口：[6, 18]，即6:00-18:00
- 车辆速度：40 km/h
- 碳排放系数：根据附件中的数据设置
- 碳价格：100元/吨CO2（假设值）

## 结果说明

优化结果将包括：
- 已选择的中转站列表及其建设成本
- 各收集点到中转站的分配关系
- 详细的路径规划方案，包括行驶顺序、距离、时间等
- 系统总成本（运输成本+建设成本+碳排放成本）
- 各垃圾类型的碳排放量和运输成本
- 各中转站的垃圾处理量

## 注意事项

- 本优化模型需要使用商业优化求解器Gurobi，请确保已安装并获得许可证
- 如果没有Gurobi许可证，可以使用开源替代方案如PuLP或SCIP
- 优化过程可能需要较长时间，特别是对于大规模实例 