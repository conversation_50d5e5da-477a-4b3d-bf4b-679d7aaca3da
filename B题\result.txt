正在从data.json文件读取数据...
JSON数据读取成功
JSON中的收集点数量: 30个
使用收集点的total_waste作为垃圾量数据
数据读取完成! 共31个点, 包括处理厂
总垃圾量: 70.00吨
理论最小车辆数: 14辆 (不考虑路径优化)
开始求解...
求解成功! 用时: 3.05秒
目标值: 0
路径 1: 0 -> 8 -> 18 -> 27 -> 24 -> 0 (距离: 0.00, 载重: 4.80吨)
路径 2: 0 -> 2 -> 25 -> 0 (距离: 0.00, 载重: 4.30吨)
路径 3: 0 -> 16 -> 28 -> 0 (距离: 0.00, 载重: 4.80吨)
路径 4: 0 -> 10 -> 30 -> 0 (距离: 0.00, 载重: 4.90吨)
路径 5: 0 -> 6 -> 13 -> 0 (距离: 0.00, 载重: 4.70吨)
路径 6: 0 -> 19 -> 3 -> 0 (距离: 0.00, 载重: 4.60吨)
路径 7: 0 -> 11 -> 17 -> 0 (距离: 0.00, 载重: 5.00吨)
路径 8: 0 -> 12 -> 7 -> 0 (距离: 0.00, 载重: 5.00吨)
路径 9: 0 -> 22 -> 5 -> 0 (距离: 0.00, 载重: 4.90吨)
路径 10: 0 -> 23 -> 0 (距离: 0.00, 载重: 3.50吨)
路径 11: 0 -> 26 -> 1 -> 0 (距离: 0.00, 载重: 4.80吨)
路径 12: 0 -> 29 -> 0 (距离: 0.00, 载重: 3.70吨)
路径 13: 0 -> 9 -> 14 -> 0 (距离: 0.00, 载重: 5.00吨)
路径 14: 0 -> 21 -> 20 -> 0 (距离: 0.00, 载重: 5.00吨)
路径 15: 0 -> 15 -> 4 -> 0 (距离: 0.00, 载重: 5.00吨)

====== 路径优化结果统计 ======
总距离: 0.00 km
总载重: 70.00 吨
使用车辆数: 15

====== 评价指标 ======
1. 平均载重率: 93.33%
2. 平均每车行驶距离: 0.00 km
3. 平均每车载重: 4.67 吨
4. 每吨垃圾的行驶距离: 0.00 km/吨
5. 平均每车访问点数: 2.00 个

====== 分布情况 ======
路径距离: 最长=0.00 km, 最短=0.00 km, 标准差=0.00
载重分布: 最大=5.00 吨, 最小=3.50 吨, 标准差=0.46

各车辆载重率:
车辆 1: 96.00% (4.80/5.00吨)
车辆 2: 86.00% (4.30/5.00吨)
车辆 3: 96.00% (4.80/5.00吨)
车辆 4: 98.00% (4.90/5.00吨)
车辆 5: 94.00% (4.70/5.00吨)
车辆 6: 92.00% (4.60/5.00吨)
车辆 7: 100.00% (5.00/5.00吨)
车辆 8: 100.00% (5.00/5.00吨)
车辆 9: 98.00% (4.90/5.00吨)
车辆 10: 70.00% (3.50/5.00吨)
车辆 11: 96.00% (4.80/5.00吨)
车辆 12: 74.00% (3.70/5.00吨)
车辆 13: 100.00% (5.00/5.00吨)
车辆 14: 100.00% (5.00/5.00吨)
车辆 15: 100.00% (5.00/5.00吨)
路径图已保存为 'cvrp_routes.png' 和 'cvrp_routes.pdf'
