#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型评估工具模块
提供各种模型评估指标和方法
"""

import numpy as np
import pandas as pd
from sklearn.metrics import (
    mean_absolute_error, mean_squared_error, r2_score,
    mean_absolute_percentage_error
)
from sklearn.model_selection import cross_val_score, TimeSeriesSplit
import matplotlib.pyplot as plt
import seaborn as sns
import os

class ModelEvaluator:
    """模型评估器"""
    
    def __init__(self):
        """初始化评估器"""
        self.evaluation_results = {}
        self.best_model = None
        
    def calculate_metrics(self, y_true, y_pred):
        """计算评估指标"""
        # 基本指标
        mae = mean_absolute_error(y_true, y_pred)
        mse = mean_squared_error(y_true, y_pred)
        rmse = np.sqrt(mse)
        r2 = r2_score(y_true, y_pred)
        
        # MAPE (平均绝对百分比误差)
        mape = np.mean(np.abs((y_true - y_pred) / (y_true + 1e-8))) * 100
        
        # SMAPE (对称平均绝对百分比误差)
        smape = np.mean(2 * np.abs(y_true - y_pred) / (np.abs(y_true) + np.abs(y_pred) + 1e-8)) * 100
        
        # WMAPE (加权平均绝对百分比误差)
        wmape = np.sum(np.abs(y_true - y_pred)) / np.sum(np.abs(y_true)) * 100
        
        # 最大误差
        max_error = np.max(np.abs(y_true - y_pred))
        
        # 平均偏差
        bias = np.mean(y_pred - y_true)
        
        # 标准化均方根误差 (NRMSE)
        nrmse = rmse / (np.max(y_true) - np.min(y_true)) * 100
        
        # 相关系数
        correlation = np.corrcoef(y_true, y_pred)[0, 1]
        
        return {
            'mae': mae,
            'mse': mse,
            'rmse': rmse,
            'r2': r2,
            'mape': mape,
            'smape': smape,
            'wmape': wmape,
            'max_error': max_error,
            'bias': bias,
            'nrmse': nrmse,
            'correlation': correlation
        }
    
    def evaluate_single_model(self, model, X_test, y_test, model_name):
        """评估单个模型"""
        try:
            # 进行预测
            if hasattr(model, 'predict'):
                y_pred = model.predict(X_test)
                
                # 如果是深度学习模型，可能返回二维数组
                if len(y_pred.shape) > 1:
                    y_pred = y_pred.flatten()
                
                # 计算指标
                metrics = self.calculate_metrics(y_test, y_pred)
                
                # 添加预测值用于后续分析
                metrics['y_pred'] = y_pred
                metrics['y_true'] = y_test
                
                return metrics
            else:
                print(f"模型 {model_name} 没有predict方法")
                return None
                
        except Exception as e:
            print(f"评估模型 {model_name} 时出错: {str(e)}")
            return None
    
    def evaluate_all_models(self, models_dict, X_test, y_test):
        """评估所有模型"""
        print("开始评估所有模型...")
        
        self.evaluation_results = {}
        
        for model_name, model in models_dict.items():
            print(f"正在评估 {model_name}...")
            
            metrics = self.evaluate_single_model(model, X_test, y_test, model_name)
            
            if metrics is not None:
                # 移除预测值（太大了，不保存在结果中）
                y_pred = metrics.pop('y_pred')
                y_true = metrics.pop('y_true')
                
                self.evaluation_results[model_name] = metrics
                
                print(f"{model_name} 评估完成:")
                print(f"  MAE: {metrics['mae']:.4f}")
                print(f"  RMSE: {metrics['rmse']:.4f}")
                print(f"  R²: {metrics['r2']:.4f}")
                print(f"  MAPE: {metrics['mape']:.2f}%")
        
        # 确定最佳模型
        self._find_best_model()
        
        return self.evaluation_results
    
    def _find_best_model(self):
        """找到最佳模型"""
        if not self.evaluation_results:
            return
        
        # 综合评分：R²权重0.4，MAPE权重0.3，RMSE权重0.3
        best_score = -float('inf')
        best_model_name = None
        
        for model_name, metrics in self.evaluation_results.items():
            # 标准化指标（0-1范围）
            r2_norm = max(0, metrics['r2'])  # R²越大越好
            mape_norm = 1 / (1 + metrics['mape'] / 100)  # MAPE越小越好
            
            # 计算RMSE的标准化分数（相对于所有模型）
            all_rmse = [m['rmse'] for m in self.evaluation_results.values()]
            rmse_norm = 1 - (metrics['rmse'] - min(all_rmse)) / (max(all_rmse) - min(all_rmse) + 1e-8)
            
            # 综合评分
            composite_score = 0.4 * r2_norm + 0.3 * mape_norm + 0.3 * rmse_norm
            
            if composite_score > best_score:
                best_score = composite_score
                best_model_name = model_name
        
        self.best_model = best_model_name
        print(f"\n最佳模型: {best_model_name} (综合评分: {best_score:.4f})")
    
    def get_best_model(self):
        """获取最佳模型名称"""
        return self.best_model
    
    def cross_validate_model(self, model, X, y, cv=5, scoring='neg_mean_squared_error'):
        """时间序列交叉验证"""
        try:
            # 使用时间序列分割
            tscv = TimeSeriesSplit(n_splits=cv)
            
            scores = cross_val_score(model, X, y, cv=tscv, scoring=scoring, n_jobs=-1)
            
            return {
                'mean_score': scores.mean(),
                'std_score': scores.std(),
                'scores': scores
            }
        except Exception as e:
            print(f"交叉验证失败: {str(e)}")
            return None
    
    def residual_analysis(self, y_true, y_pred, model_name, save_dir='results/plots'):
        """残差分析"""
        residuals = y_true - y_pred
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle(f'{model_name} 残差分析', fontsize=16, fontweight='bold')
        
        # 残差散点图
        axes[0, 0].scatter(y_pred, residuals, alpha=0.6)
        axes[0, 0].axhline(y=0, color='red', linestyle='--')
        axes[0, 0].set_xlabel('预测值')
        axes[0, 0].set_ylabel('残差')
        axes[0, 0].set_title('残差 vs 预测值')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 残差直方图
        axes[0, 1].hist(residuals, bins=30, alpha=0.7, edgecolor='black')
        axes[0, 1].set_xlabel('残差')
        axes[0, 1].set_ylabel('频次')
        axes[0, 1].set_title('残差分布')
        axes[0, 1].grid(True, alpha=0.3)
        
        # Q-Q图
        from scipy import stats
        stats.probplot(residuals, dist="norm", plot=axes[1, 0])
        axes[1, 0].set_title('Q-Q图')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 预测值 vs 真实值
        axes[1, 1].scatter(y_true, y_pred, alpha=0.6)
        min_val = min(y_true.min(), y_pred.min())
        max_val = max(y_true.max(), y_pred.max())
        axes[1, 1].plot([min_val, max_val], [min_val, max_val], 'red', linestyle='--')
        axes[1, 1].set_xlabel('真实值')
        axes[1, 1].set_ylabel('预测值')
        axes[1, 1].set_title('预测值 vs 真实值')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图片
        os.makedirs(save_dir, exist_ok=True)
        plt.savefig(os.path.join(save_dir, f'{model_name}_residual_analysis.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
    
    def prediction_interval(self, y_pred, confidence_level=0.95):
        """计算预测区间"""
        # 简化的预测区间计算（基于残差标准差）
        if not hasattr(self, 'residual_std'):
            # 如果没有残差标准差，使用预测值的标准差作为近似
            self.residual_std = np.std(y_pred) * 0.1  # 假设10%的不确定性
        
        # 计算置信区间
        from scipy import stats
        alpha = 1 - confidence_level
        z_score = stats.norm.ppf(1 - alpha/2)
        
        margin_of_error = z_score * self.residual_std
        
        lower_bound = y_pred - margin_of_error
        upper_bound = y_pred + margin_of_error
        
        return lower_bound, upper_bound
    
    def generate_evaluation_report(self, save_path='results/evaluation_report.txt'):
        """生成评估报告"""
        if not self.evaluation_results:
            print("没有评估结果可生成报告")
            return
        
        with open(save_path, 'w', encoding='utf-8') as f:
            f.write("模型评估报告\n")
            f.write("=" * 50 + "\n\n")
            
            f.write("评估指标说明:\n")
            f.write("- MAE: 平均绝对误差，越小越好\n")
            f.write("- RMSE: 均方根误差，越小越好\n")
            f.write("- R²: 决定系数，越接近1越好\n")
            f.write("- MAPE: 平均绝对百分比误差，越小越好\n")
            f.write("- SMAPE: 对称平均绝对百分比误差，越小越好\n")
            f.write("- WMAPE: 加权平均绝对百分比误差，越小越好\n")
            f.write("- NRMSE: 标准化均方根误差，越小越好\n")
            f.write("- Bias: 平均偏差，越接近0越好\n")
            f.write("- Correlation: 相关系数，越接近1越好\n\n")
            
            f.write("详细评估结果:\n")
            f.write("-" * 50 + "\n")
            
            # 按R²排序
            sorted_models = sorted(self.evaluation_results.items(), 
                                 key=lambda x: x[1]['r2'], reverse=True)
            
            for model_name, metrics in sorted_models:
                f.write(f"\n{model_name}:\n")
                f.write(f"  MAE: {metrics['mae']:.6f}\n")
                f.write(f"  RMSE: {metrics['rmse']:.6f}\n")
                f.write(f"  R²: {metrics['r2']:.6f}\n")
                f.write(f"  MAPE: {metrics['mape']:.4f}%\n")
                f.write(f"  SMAPE: {metrics['smape']:.4f}%\n")
                f.write(f"  WMAPE: {metrics['wmape']:.4f}%\n")
                f.write(f"  NRMSE: {metrics['nrmse']:.4f}%\n")
                f.write(f"  Bias: {metrics['bias']:.6f}\n")
                f.write(f"  Correlation: {metrics['correlation']:.6f}\n")
                f.write(f"  Max Error: {metrics['max_error']:.6f}\n")
            
            if self.best_model:
                f.write(f"\n推荐模型: {self.best_model}\n")
                best_metrics = self.evaluation_results[self.best_model]
                f.write(f"该模型在综合评估中表现最佳，R²={best_metrics['r2']:.4f}, "
                       f"MAPE={best_metrics['mape']:.2f}%\n")
        
        print(f"评估报告已保存到: {save_path}")
    
    def compare_models_statistically(self, models_dict, X, y, n_splits=5):
        """统计学模型比较"""
        print("进行统计学模型比较...")
        
        tscv = TimeSeriesSplit(n_splits=n_splits)
        comparison_results = {}
        
        for model_name, model in models_dict.items():
            try:
                scores = []
                for train_idx, test_idx in tscv.split(X):
                    X_train, X_test = X[train_idx], X[test_idx]
                    y_train, y_test = y[train_idx], y[test_idx]
                    
                    # 训练模型
                    model.fit(X_train, y_train)
                    
                    # 预测
                    y_pred = model.predict(X_test)
                    if len(y_pred.shape) > 1:
                        y_pred = y_pred.flatten()
                    
                    # 计算R²
                    r2 = r2_score(y_test, y_pred)
                    scores.append(r2)
                
                comparison_results[model_name] = {
                    'mean_r2': np.mean(scores),
                    'std_r2': np.std(scores),
                    'scores': scores
                }
                
                print(f"{model_name}: R² = {np.mean(scores):.4f} ± {np.std(scores):.4f}")
                
            except Exception as e:
                print(f"统计比较 {model_name} 失败: {str(e)}")
                continue
        
        return comparison_results

if __name__ == "__main__":
    # 测试评估工具
    from sklearn.datasets import make_regression
    from sklearn.ensemble import RandomForestRegressor
    from sklearn.linear_model import LinearRegression
    from sklearn.model_selection import train_test_split
    
    # 生成测试数据
    X, y = make_regression(n_samples=1000, n_features=10, noise=0.1, random_state=42)
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # 训练测试模型
    models = {
        'Random_Forest': RandomForestRegressor(n_estimators=50, random_state=42),
        'Linear_Regression': LinearRegression()
    }
    
    for name, model in models.items():
        model.fit(X_train, y_train)
    
    # 创建评估器
    evaluator = ModelEvaluator()
    
    # 评估所有模型
    results = evaluator.evaluate_all_models(models, X_test, y_test)
    
    print("\n评估完成!")
    print(f"最佳模型: {evaluator.get_best_model()}")
    
    # 生成报告
    evaluator.generate_evaluation_report('test_evaluation_report.txt')
