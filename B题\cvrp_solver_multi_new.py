#!/usr/bin/env python
# coding: utf-8
"""
CVRP求解器 - 第二问解决方案
多车辆协同与载重-容积双约束的优化问题
每种垃圾类型分别建模，并分别求解
使用新数据源data_new.json
"""

import math
import time
import json
import sys
import numpy as np
import matplotlib.pyplot as plt
from ortools.constraint_solver import routing_enums_pb2
from ortools.constraint_solver import pywrapcp
import matplotlib.patches as mpatches

# 重定向输出到文件
output_file = open('result_q2_new.txt', 'w', encoding='utf-8')
def print_and_log(message):
    """同时打印到控制台和日志文件"""
    print(message)
    output_file.write(message + '\n')
    output_file.flush()  # 实时写入文件

def create_data_model():
    """准备数据模型 - 从新的JSON文件读取数据，获取4种垃圾类型的分布和车辆信息"""
    data = {}
    
    print_and_log("正在从data_new.json文件读取数据...")
    try:
        with open('data_new.json', 'r', encoding='utf-8') as f:
            json_data = json.load(f)
        print_and_log("JSON数据读取成功")
    except Exception as e:
        print_and_log(f"读取JSON数据失败: {e}")
        sys.exit(1)
    
    # 处理厂坐标从collection_points中获取
    locations = []
    collection_points = json_data['collection_points']
    print_and_log(f"JSON中的收集点数量: {len(collection_points)}个")
    
    # 按id排序收集点
    sorted_points = sorted(collection_points, key=lambda p: p['id'])
    
    for point in sorted_points:
        locations.append((point['x'], point['y']))
    
    # 读取车辆信息
    vehicles = json_data['vehicles']
    waste_types = ['kitchen_waste', 'recyclable_waste', 'hazardous_waste', 'other_waste']
    waste_names = ['厨余垃圾', '可回收物', '有害垃圾', '其他垃圾']
    type_colors = ['green', 'blue', 'red', 'orange']  # 四种垃圾类型的颜色
    
    # 读取垃圾分布信息
    waste_distribution = json_data['waste_distribution']
    
    # 准备每种垃圾类型的需求数据
    demands_by_type = {}
    
    # 对每个垃圾类型，创建需求列表
    for idx, waste_type in enumerate(waste_types):
        demands = []
        
        for point in sorted_points:
            point_id = point['id']
            # 找到该点的垃圾分布数据
            waste_data = next((item for item in waste_distribution if item['collection_point_id'] == point_id), None)
            if waste_data:
                demands.append(waste_data[waste_type])
            else:
                demands.append(0)  # 如果没有找到对应的分布数据，设为0
        
        demands_by_type[waste_type] = demands
    
    # 提取车辆容量和容积限制
    vehicle_capacities = {}  # 吨
    vehicle_volumes = {}     # 立方米
    vehicle_costs = {}       # 单位距离成本
    
    for vehicle in vehicles:
        vehicle_type = vehicle['waste_type']
        vehicle_capacities[vehicle_type] = vehicle['capacity']
        vehicle_volumes[vehicle_type] = vehicle['volume']
        vehicle_costs[vehicle_type] = vehicle['unit_cost']
    
    # 设置数据模型
    data['locations'] = locations
    data['demands_by_type'] = demands_by_type
    data['waste_types'] = waste_types
    data['waste_names'] = waste_names
    data['type_colors'] = type_colors
    data['vehicle_capacities'] = vehicle_capacities
    data['vehicle_volumes'] = vehicle_volumes
    data['vehicle_costs'] = vehicle_costs
    data['depot'] = 0
    
    # 为每种垃圾类型计算总量和最小车辆数
    min_vehicles_by_type = {}
    total_waste_by_type = {}
    
    for waste_type in waste_types:
        total_demand = sum(demands_by_type[waste_type])
        total_waste_by_type[waste_type] = total_demand
        
        # 计算最小车辆数 (同时考虑重量和体积约束)
        weight_based = math.ceil(total_demand / vehicle_capacities[waste_type])
        
        # 为体积约束计算最小车辆数，需要先确定每个点的垃圾体积
        total_volume = 0
        # 获取第一个收集点的密度数据
        density = waste_distribution[0]['densities'][waste_type]  # 密度 吨/立方米
        
        for demand in demands_by_type[waste_type][1:]:  # 跳过处理厂
            volume = demand / density  # 体积 = 重量/密度
            total_volume += volume
        
        volume_based = math.ceil(total_volume / vehicle_volumes[waste_type])
        
        # 取重量和体积约束的较大值
        min_vehicles = max(weight_based, volume_based)
        min_vehicles_by_type[waste_type] = min_vehicles
        
        # 设置足够的车辆数量，为理论最小值的2倍，确保有足够的车辆
        vehicles_count = min_vehicles * 2
        data[f'num_vehicles_{waste_type}'] = vehicles_count
    
    data['min_vehicles_by_type'] = min_vehicles_by_type
    data['total_waste_by_type'] = total_waste_by_type
    
    # 计算每个点每种垃圾的密度和体积
    densities = {}
    volumes_by_type = {}
    
    for waste_type in waste_types:
        densities[waste_type] = waste_distribution[0]['densities'][waste_type]
        volumes = [0]  # 处理厂体积为0
        
        for demand in demands_by_type[waste_type][1:]:  # 跳过处理厂
            volume = demand / densities[waste_type]  # 体积 = 重量/密度
            volumes.append(volume)
        
        volumes_by_type[waste_type] = volumes
    
    data['densities'] = densities
    data['volumes_by_type'] = volumes_by_type
    
    print_and_log(f"数据读取完成! 共{len(locations)}个点, 包括处理厂")
    
    for waste_type, waste_name in zip(waste_types, waste_names):
        print_and_log(f"{waste_name}总量: {total_waste_by_type[waste_type]:.2f}吨, "
                     f"体积: {total_waste_by_type[waste_type]/densities[waste_type]:.2f}立方米, "
                     f"理论最小车辆数: {min_vehicles_by_type[waste_type]}辆")
    
    return data

def compute_euclidean_distance_matrix(locations):
    """计算欧氏距离矩阵"""
    distances = {}
    for from_node in range(len(locations)):
        distances[from_node] = {}
        for to_node in range(len(locations)):
            if from_node == to_node:
                distances[from_node][to_node] = 0
            else:
                # 计算欧氏距离
                distances[from_node][to_node] = (
                    math.sqrt(
                        (locations[from_node][0] - locations[to_node][0]) ** 2 +
                        (locations[from_node][1] - locations[to_node][1]) ** 2))
    return distances

def solve_cvrp_for_type(data, waste_type, waste_name, distance_matrix):
    """为单个垃圾类型求解CVRP问题"""
    print_and_log(f"\n====== 正在求解 {waste_name} 路径优化 ======")
    
    # 提取相关数据
    demands = data['demands_by_type'][waste_type]
    volumes = data['volumes_by_type'][waste_type]
    vehicle_capacity = data['vehicle_capacities'][waste_type]
    vehicle_volume = data['vehicle_volumes'][waste_type]
    num_vehicles = data[f'num_vehicles_{waste_type}']
    unit_cost = data['vehicle_costs'][waste_type]
    
    # 创建路由索引管理器
    manager = pywrapcp.RoutingIndexManager(len(data['locations']),
                                          num_vehicles, data['depot'])
    
    # 创建路由模型
    routing = pywrapcp.RoutingModel(manager)
    
    # 注册距离回调函数
    def distance_callback(from_index, to_index):
        from_node = manager.IndexToNode(from_index)
        to_node = manager.IndexToNode(to_index)
        return int(distance_matrix[from_node][to_node] * 1000)  # 转换为整数，乘以1000保留精度
    
    transit_callback_index = routing.RegisterTransitCallback(distance_callback)
    
    # 定义距离成本 (乘以单位距离成本)
    routing.SetArcCostEvaluatorOfAllVehicles(transit_callback_index)
    
    # 添加重量容量约束
    def demand_callback(from_index):
        from_node = manager.IndexToNode(from_index)
        return int(demands[from_node] * 1000)  # 转为整数，乘以1000保留精度
    
    demand_callback_index = routing.RegisterUnaryTransitCallback(demand_callback)
    routing.AddDimensionWithVehicleCapacity(
        demand_callback_index,
        0,  # null capacity slack
        [int(vehicle_capacity * 1000)] * num_vehicles,  # 车辆重量容量
        True,  # 从起始位置开始计算
        'Weight')
    
    # 添加体积容量约束
    def volume_callback(from_index):
        from_node = manager.IndexToNode(from_index)
        return int(volumes[from_node] * 1000)  # 转为整数，乘以1000保留精度
    
    volume_callback_index = routing.RegisterUnaryTransitCallback(volume_callback)
    routing.AddDimensionWithVehicleCapacity(
        volume_callback_index,
        0,  # null capacity slack
        [int(vehicle_volume * 1000)] * num_vehicles,  # 车辆体积容量
        True,  # 从起始位置开始计算
        'Volume')
    
    # 设置求解策略
    search_parameters = pywrapcp.DefaultRoutingSearchParameters()
    search_parameters.first_solution_strategy = (
        routing_enums_pb2.FirstSolutionStrategy.PATH_CHEAPEST_ARC)
    search_parameters.local_search_metaheuristic = (
        routing_enums_pb2.LocalSearchMetaheuristic.GUIDED_LOCAL_SEARCH)
    search_parameters.time_limit.seconds = 30  # 每种垃圾类型的求解时间限制，减少到30秒使得程序能够更快完成
    
    # 求解问题
    start_time = time.time()
    solution = routing.SolveWithParameters(search_parameters)
    solve_time = time.time() - start_time
    
    # 处理结果
    if solution:
        print_and_log(f"求解成功! 用时: {solve_time:.2f}秒")
        return solution, routing, manager, solve_time
    else:
        print_and_log(f"没有找到解! 用时: {solve_time:.2f}秒")
        return None, routing, manager, solve_time

def print_solution_for_type(data, solution, routing, manager, waste_type, waste_name):
    """为单个垃圾类型输出解决方案并返回路径数据"""
    print_and_log(f"\n====== {waste_name} 路径优化结果 ======")
    print_and_log(f'目标值: {solution.ObjectiveValue() / 1000:.2f}')
    
    demands = data['demands_by_type'][waste_type]
    volumes = data['volumes_by_type'][waste_type]
    vehicle_capacity = data['vehicle_capacities'][waste_type]
    vehicle_volume = data['vehicle_volumes'][waste_type]
    unit_cost = data['vehicle_costs'][waste_type]
    num_vehicles = data[f'num_vehicles_{waste_type}']
    
    total_distance = 0
    total_load = 0
    total_volume = 0
    total_cost = 0
    
    # 统计使用车辆数 (实际路径数)
    used_vehicles = 0
    
    # 收集路径数据用于绘图
    routes_for_plot = []
    loads = []
    volumes = []
    distances = []
    
    for vehicle_id in range(num_vehicles):
        index = routing.Start(vehicle_id)
        # 检查是否使用了该车辆
        if solution.Value(routing.NextVar(index)) == routing.End(vehicle_id):
            continue  # 该车辆未使用
            
        used_vehicles += 1
        plan_output = f'路径 {used_vehicles}:'
        route_distance = 0
        route_load = 0
        route_volume = 0
        
        route_for_plot = []
        
        while not routing.IsEnd(index):
            node_index = manager.IndexToNode(index)
            route_load += demands[node_index]
            route_volume += data['volumes_by_type'][waste_type][node_index]
            plan_output += f' {node_index} ->'
            route_for_plot.append(node_index)
            
            previous_index = index
            index = solution.Value(routing.NextVar(index))
            route_distance += routing.GetArcCostForVehicle(
                previous_index, index, vehicle_id) / 1000  # 转回实际距离
                
        node_index = manager.IndexToNode(index)
        route_for_plot.append(node_index)  # 添加返回处理厂的路径
        
        route_cost = route_distance * unit_cost  # 路径成本 = 距离 * 单位成本
        
        plan_output += f' {node_index}'
        plan_output += f' (距离: {route_distance:.2f}km, 重量: {route_load:.2f}吨, 体积: {route_volume:.2f}m³, 成本: {route_cost:.2f}元)'
        print_and_log(plan_output)
        
        total_distance += route_distance
        total_load += route_load
        total_volume += route_volume
        total_cost += route_cost
        
        # 保存绘图数据
        routes_for_plot.append(route_for_plot)
        loads.append(route_load)
        volumes.append(route_volume)
        distances.append(route_distance)
    
    # 计算各种评价指标
    # 1. 平均载重率
    avg_weight_ratio = (total_load / (used_vehicles * vehicle_capacity)) * 100 if used_vehicles > 0 else 0
    
    # 2. 平均体积利用率
    avg_volume_ratio = (total_volume / (used_vehicles * vehicle_volume)) * 100 if used_vehicles > 0 else 0
    
    # 3. 平均每车行驶距离
    avg_distance_per_vehicle = total_distance / used_vehicles if used_vehicles > 0 else 0
    
    # 4. 平均每车载重和体积
    avg_load_per_vehicle = total_load / used_vehicles if used_vehicles > 0 else 0
    avg_volume_per_vehicle = total_volume / used_vehicles if used_vehicles > 0 else 0
    
    # 5. 每吨垃圾的行驶距离和成本
    distance_per_ton = total_distance / total_load if total_load > 0 else 0
    cost_per_ton = total_cost / total_load if total_load > 0 else 0
    
    # 输出基本统计数据
    print_and_log('\n====== 路径优化结果统计 ======')
    print_and_log(f'总距离: {total_distance:.2f} km')
    print_and_log(f'总载重: {total_load:.2f} 吨')
    print_and_log(f'总体积: {total_volume:.2f} m³')
    print_and_log(f'总成本: {total_cost:.2f} 元')
    print_and_log(f'使用车辆数: {used_vehicles}')
    
    # 输出评价指标
    print_and_log('\n====== 评价指标 ======')
    print_and_log(f'1. 平均载重率: {avg_weight_ratio:.2f}%')
    print_and_log(f'2. 平均体积利用率: {avg_volume_ratio:.2f}%')
    print_and_log(f'3. 平均每车行驶距离: {avg_distance_per_vehicle:.2f} km')
    print_and_log(f'4. 平均每车载重: {avg_load_per_vehicle:.2f} 吨')
    print_and_log(f'5. 平均每车体积: {avg_volume_per_vehicle:.2f} m³')
    print_and_log(f'6. 每吨垃圾的行驶距离: {distance_per_ton:.2f} km/吨')
    print_and_log(f'7. 每吨垃圾的运输成本: {cost_per_ton:.2f} 元/吨')
    
    # 输出载重率和体积率
    print_and_log("\n各车辆资源利用率:")
    for i in range(len(routes_for_plot)):
        load_ratio = (loads[i] / vehicle_capacity) * 100
        vol_ratio = (volumes[i] / vehicle_volume) * 100
        print_and_log(f'车辆 {i+1}: 载重率={load_ratio:.2f}% ({loads[i]:.2f}/{vehicle_capacity:.2f}吨), '
                    f'体积率={vol_ratio:.2f}% ({volumes[i]:.2f}/{vehicle_volume:.2f}m³)')
    
    return routes_for_plot, total_distance, total_cost, used_vehicles, avg_weight_ratio, avg_volume_ratio, distance_per_ton

def plot_routes_for_type(data, routes, waste_type, waste_name, type_color):
    """为单个垃圾类型绘制路径图"""
    plt.figure(figsize=(12, 10))
    
    # 设置中文字体支持
    try:
        # 尝试设置中文字体
        import matplotlib.font_manager as fm
        # 检查系统中文字体
        chinese_fonts = [f.name for f in fm.fontManager.ttflist if 'simhei' in f.name.lower() or 'microsoft yahei' in f.name.lower() or 'simsun' in f.name.lower() or 'fangsong' in f.name.lower() or 'kaiti' in f.name.lower()]
        if chinese_fonts:
            plt.rcParams['font.family'] = chinese_fonts[0]
        else:
            print_and_log("未找到支持中文的字体，将使用英文标题")
    except:
        print_and_log("设置中文字体失败，将使用英文标题")
    
    # 绘制所有点
    locations = data['locations']
    depot_x, depot_y = locations[0]
    
    # 绘制收集点
    plt.scatter([loc[0] for loc in locations[1:]], 
                [loc[1] for loc in locations[1:]],
                c='black', s=50, label='收集点')
    
    # 突出显示处理厂
    plt.scatter(depot_x, depot_y, c='red', s=200, marker='*', label='处理厂 (0)')
    
    # 为每个点添加编号
    for i, loc in enumerate(locations):
        plt.annotate(str(i), (loc[0], loc[1]), xytext=(5, 5), 
                     textcoords='offset points', fontsize=10)
    
    # 使用不同线型区分同类型的不同路径
    linestyles = ['-', '--', '-.', ':']
    
    # 绘制该类型的路径
    for i, route in enumerate(routes):
        route_x = [locations[j][0] for j in route]
        route_y = [locations[j][1] for j in route]
        
        linestyle = linestyles[i % len(linestyles)]
        plt.plot(route_x, route_y, c=type_color, linewidth=2.5, linestyle=linestyle, 
                label=f'路径 {i+1}')
    
    # 添加标题和标签
    plt.title(f'{waste_name}路径优化方案', fontsize=16)
    plt.xlabel('X坐标 (km)', fontsize=12)
    plt.ylabel('Y坐标 (km)', fontsize=12)
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 添加图例 - 限制图例项数量
    if len(routes) > 6:
        # 如果路径太多，使用两行显示图例
        plt.legend(loc='upper center', bbox_to_anchor=(0.5, -0.12),
                  fancybox=True, shadow=True, ncol=min(6, len(routes)))
    else:
        plt.legend(loc='best')
    
    # 保存图片
    filename_base = f'cvrp_routes_{waste_type}_new'
    plt.savefig(f'{filename_base}.png', dpi=300, bbox_inches='tight')
    plt.savefig(f'{filename_base}.pdf', bbox_inches='tight')
    plt.close()
    
    print_and_log(f"{waste_name}路径图已保存为 '{filename_base}.png' 和 '{filename_base}.pdf'")

def plot_routes_by_type(data, all_routes, waste_types):
    """绘制所有垃圾类型的路径图"""
    # 首先为每种垃圾类型单独绘制路径图
    for type_idx, waste_type in enumerate(waste_types):
        if not all_routes[waste_type]:  # 跳过没有路径的类型
            continue
            
        waste_name = data['waste_names'][type_idx]
        routes = all_routes[waste_type]
        type_color = data['type_colors'][type_idx]
        
        # 为该类型绘制单独的路径图
        plot_routes_for_type(data, routes, waste_type, waste_name, type_color)
    
    # 然后绘制包含所有类型的总路径图
    plt.figure(figsize=(15, 12))
    
    # 设置中文字体支持
    try:
        # 尝试设置中文字体
        import matplotlib.font_manager as fm
        # 检查系统中文字体
        chinese_fonts = [f.name for f in fm.fontManager.ttflist if 'simhei' in f.name.lower() or 'microsoft yahei' in f.name.lower() or 'simsun' in f.name.lower() or 'fangsong' in f.name.lower() or 'kaiti' in f.name.lower()]
        if chinese_fonts:
            plt.rcParams['font.family'] = chinese_fonts[0]
        else:
            print_and_log("未找到支持中文的字体，将使用英文标题")
    except:
        print_and_log("设置中文字体失败，将使用英文标题")
    
    # 绘制所有点
    locations = data['locations']
    depot_x, depot_y = locations[0]
    
    # 绘制收集点
    plt.scatter([loc[0] for loc in locations[1:]], 
                [loc[1] for loc in locations[1:]],
                c='black', s=50, label='收集点')
    
    # 突出显示处理厂
    plt.scatter(depot_x, depot_y, c='red', s=200, marker='*', label='处理厂 (0)')
    
    # 为每个点添加编号
    for i, loc in enumerate(locations):
        plt.annotate(str(i), (loc[0], loc[1]), xytext=(5, 5), 
                     textcoords='offset points', fontsize=10)
    
    # 为每种垃圾类型绘制路径，添加偏移量避免重叠
    legend_handles = []
    offset_scale = 0.3  # 偏移量比例，设置更大以明显看出不同路径
    
    for type_idx, waste_type in enumerate(waste_types):
        if not all_routes[waste_type]:  # 跳过没有路径的类型
            continue
            
        waste_name = data['waste_names'][type_idx]
        routes = all_routes[waste_type]
        type_color = data['type_colors'][type_idx]
        
        # 为当前类型计算偏移方向
        angle = (type_idx * np.pi/2) + np.pi/4  # 沿不同方向偏移
        offset_x = np.cos(angle) * offset_scale
        offset_y = np.sin(angle) * offset_scale
        
        # 使用不同线型和偏移量区分同类型的不同路径
        linestyles = ['-', '--', '-.', ':']
        
        for i, route in enumerate(routes):
            # 对每个点应用偏移（除了处理厂）
            route_x = []
            route_y = []
            
            for j in route:
                if j == 0:  # 处理厂不偏移
                    route_x.append(locations[j][0])
                    route_y.append(locations[j][1])
                else:
                    # 给该点添加偏移
                    route_x.append(locations[j][0] + offset_x)
                    route_y.append(locations[j][1] + offset_y)
            
            linestyle = linestyles[i % len(linestyles)]
            plt.plot(route_x, route_y, c=type_color, linewidth=2, linestyle=linestyle)
        
        # 为垃圾类型添加图例项
        patch = mpatches.Patch(color=type_color, label=waste_name)
        legend_handles.append(patch)
    
    # 添加标题和标签
    plt.title('城市垃圾分类运输的多车辆路径优化方案', fontsize=16)
    plt.xlabel('X坐标 (km)', fontsize=12)
    plt.ylabel('Y坐标 (km)', fontsize=12)
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 添加图例
    plt.legend(handles=legend_handles, loc='best', fontsize=12)
    
    # 添加说明性注释
    plt.figtext(0.5, 0.01, '注: 为清晰显示不同垃圾类型的路径，各路线有轻微偏移', 
                ha='center', fontsize=10, bbox=dict(facecolor='white', alpha=0.8))
    
    # 保存图片
    plt.savefig('cvrp_multi_routes_new.png', dpi=300, bbox_inches='tight')
    plt.savefig('cvrp_multi_routes_new.pdf', bbox_inches='tight')
    plt.close()
    
    print_and_log("多类型路径图已保存为 'cvrp_multi_routes_new.png' 和 'cvrp_multi_routes_new.pdf'")

def main():
    """问题2求解主函数"""
    try:
        # 准备数据
        data = create_data_model()
        
        # 计算距离矩阵
        distance_matrix = compute_euclidean_distance_matrix(data['locations'])
        
        # 存储所有类型的求解结果
        all_routes = {}
        all_stats = {}
        total_system_distance = 0
        total_system_cost = 0
        total_vehicles_used = 0
        
        # 为每种垃圾类型单独求解CVRP
        for idx, waste_type in enumerate(data['waste_types']):
            waste_name = data['waste_names'][idx]
            
            # 求解当前垃圾类型的CVRP
            solution, routing, manager, solve_time = solve_cvrp_for_type(
                data, waste_type, waste_name, distance_matrix)
            
            if solution:
                # 输出结果并获取统计数据
                routes, distance, cost, vehicles, weight_ratio, volume_ratio, dist_per_ton = print_solution_for_type(
                    data, solution, routing, manager, waste_type, waste_name)
                
                # 存储结果
                all_routes[waste_type] = routes
                all_stats[waste_type] = {
                    'distance': distance,
                    'cost': cost,
                    'vehicles': vehicles,
                    'weight_ratio': weight_ratio,
                    'volume_ratio': volume_ratio,
                    'dist_per_ton': dist_per_ton
                }
                
                total_system_distance += distance
                total_system_cost += cost
                total_vehicles_used += vehicles
            else:
                print_and_log(f"警告: {waste_name}没有找到可行解!")
                all_routes[waste_type] = []
                all_stats[waste_type] = {'distance': 0, 'cost': 0, 'vehicles': 0}
        
        # 绘制所有路径
        plot_routes_by_type(data, all_routes, data['waste_types'])
        
        # 输出系统总体统计数据
        print_and_log("\n====== 系统整体性能 ======")
        print_and_log(f"总行驶距离: {total_system_distance:.2f} km")
        print_and_log(f"总运输成本: {total_system_cost:.2f} 元")
        print_and_log(f"总使用车辆: {total_vehicles_used} 辆")
        
        print_and_log("\n各垃圾类型统计:")
        
        for idx, waste_type in enumerate(data['waste_types']):
            waste_name = data['waste_names'][idx]
            stats = all_stats[waste_type]
            print_and_log(f"{waste_name}: 距离={stats['distance']:.2f}km, "
                        f"成本={stats['cost']:.2f}元, "
                        f"车辆={stats['vehicles']}辆, "
                        f"载重率={stats['weight_ratio']:.2f}%, "
                        f"体积率={stats['volume_ratio']:.2f}%")
        
        # 保存简要结果到单独文件
        with open('summary_q2_new.txt', 'w', encoding='utf-8') as f:
            f.write(f"CVRP问题2求解摘要 - 多车辆协同与载重-容积双约束的优化\n")
            f.write(f"===========================================\n")
            f.write(f"总行驶距离: {total_system_distance:.2f} km\n")
            f.write(f"总运输成本: {total_system_cost:.2f} 元\n")
            f.write(f"总使用车辆: {total_vehicles_used} 辆\n\n")
            
            f.write("各垃圾类型统计:\n")
            for idx, waste_type in enumerate(data['waste_types']):
                waste_name = data['waste_names'][idx]
                stats = all_stats[waste_type]
                cap = data['vehicle_capacities'][waste_type]
                vol = data['vehicle_volumes'][waste_type]
                unit_cost = data['vehicle_costs'][waste_type]
                
                f.write(f"{waste_name}:\n")
                f.write(f"  距离: {stats['distance']:.2f} km\n")
                f.write(f"  成本: {stats['cost']:.2f} 元\n")
                f.write(f"  车辆: {stats['vehicles']} 辆\n")
                f.write(f"  载重限制: {cap} 吨\n")
                f.write(f"  体积限制: {vol} m³\n")
                f.write(f"  单位成本: {unit_cost} 元/km\n")
                f.write(f"  载重率: {stats['weight_ratio']:.2f}%\n")
                f.write(f"  体积率: {stats['volume_ratio']:.2f}%\n\n")
    
    except Exception as e:
        print_and_log(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc(file=output_file)
    
    finally:
        # 关闭输出文件
        output_file.close()
        print("结果已保存到result_q2_new.txt文件中")

if __name__ == '__main__':
    main() 