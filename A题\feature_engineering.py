#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特征工程模块
处理光伏发电数据的特征提取和工程化
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.impute import SimpleImputer

class FeatureEngineer:
    """特征工程类"""
    
    def __init__(self):
        """初始化特征工程器"""
        self.scaler = StandardScaler()
        self.target_scaler = MinMaxScaler()
        self.imputer = SimpleImputer(strategy='mean')
        self.feature_columns = []
        self.target_column = 'power_output'
        
    def create_features(self, data):
        """创建所有特征"""
        print("开始特征工程...")
        
        # 复制数据避免修改原始数据
        df = data.copy()
        
        # 确保datetime列是datetime类型
        if not pd.api.types.is_datetime64_any_dtype(df['datetime']):
            df['datetime'] = pd.to_datetime(df['datetime'])
        
        # 按时间排序
        df = df.sort_values('datetime').reset_index(drop=True)
        
        # 1. 时间特征
        df = self._create_time_features(df)
        
        # 2. 气象特征工程
        df = self._create_weather_features(df)
        
        # 3. 滞后特征
        df = self._create_lag_features(df)
        
        # 4. 滑动窗口特征
        df = self._create_rolling_features(df)
        
        # 5. 交互特征
        df = self._create_interaction_features(df)
        
        # 6. 周期性特征
        df = self._create_cyclical_features(df)
        
        # 7. 处理缺失值
        df = self._handle_missing_values(df)
        
        print(f"特征工程完成，共创建 {len(df.columns)} 个特征")
        return df
    
    def _create_time_features(self, df):
        """创建时间特征"""
        print("创建时间特征...")
        
        # 基础时间特征
        df['year'] = df['datetime'].dt.year
        df['month'] = df['datetime'].dt.month
        df['day'] = df['datetime'].dt.day
        df['hour'] = df['datetime'].dt.hour
        df['minute'] = df['datetime'].dt.minute
        df['weekday'] = df['datetime'].dt.weekday
        df['dayofyear'] = df['datetime'].dt.dayofyear
        df['week'] = df['datetime'].dt.isocalendar().week
        df['quarter'] = df['datetime'].dt.quarter
        
        # 是否为周末
        df['is_weekend'] = (df['weekday'] >= 5).astype(int)
        
        # 季节特征
        df['season'] = df['month'].map({
            12: 0, 1: 0, 2: 0,  # 冬季
            3: 1, 4: 1, 5: 1,   # 春季
            6: 2, 7: 2, 8: 2,   # 夏季
            9: 3, 10: 3, 11: 3  # 秋季
        })
        
        # 时间段特征
        df['time_period'] = pd.cut(df['hour'], 
                                  bins=[0, 6, 12, 18, 24], 
                                  labels=['night', 'morning', 'afternoon', 'evening'],
                                  include_lowest=True)
        df['time_period'] = df['time_period'].cat.codes
        
        # 日出日落时间（简化计算）
        df['sunrise_hour'] = 6 + 2 * np.sin(2 * np.pi * df['dayofyear'] / 365.25)
        df['sunset_hour'] = 18 + 2 * np.sin(2 * np.pi * df['dayofyear'] / 365.25)
        
        # 是否为日照时间
        df['is_daylight'] = ((df['hour'] >= df['sunrise_hour']) & 
                            (df['hour'] <= df['sunset_hour'])).astype(int)
        
        # 距离日出日落的时间
        df['hours_from_sunrise'] = df['hour'] - df['sunrise_hour']
        df['hours_to_sunset'] = df['sunset_hour'] - df['hour']
        
        return df
    
    def _create_weather_features(self, df):
        """创建气象特征"""
        print("创建气象特征...")
        
        # 温度相关特征
        df['temp_squared'] = df['temperature'] ** 2
        df['temp_deviation'] = df['temperature'] - df['temperature'].rolling(24).mean()
        
        # 辐照度相关特征
        df['irradiance_per_temp'] = df['solar_irradiance'] / (df['temperature'] + 273.15)
        df['irradiance_normalized'] = df['solar_irradiance'] / 1000  # 标准化到STC条件
        
        # 湿度特征
        df['humidity_squared'] = df['humidity'] ** 2
        df['is_high_humidity'] = (df['humidity'] > 80).astype(int)
        
        # 风速特征
        df['wind_speed_squared'] = df['wind_speed'] ** 2
        df['wind_category'] = pd.cut(df['wind_speed'], 
                                   bins=[0, 2, 5, 10, float('inf')], 
                                   labels=[0, 1, 2, 3])
        
        # 云量特征
        df['is_cloudy'] = (df['cloud_cover'] > 5).astype(int)
        df['is_clear'] = (df['cloud_cover'] < 2).astype(int)
        
        # 降水特征
        df['is_raining'] = (df['precipitation'] > 0.1).astype(int)
        df['rain_intensity'] = pd.cut(df['precipitation'], 
                                    bins=[0, 0.1, 2, 10, float('inf')], 
                                    labels=[0, 1, 2, 3])
        
        # 大气压力特征
        df['pressure_deviation'] = df['pressure'] - df['pressure'].mean()
        
        # 天气综合指数
        df['weather_index'] = (df['solar_irradiance'] / 1000 * 
                              (1 - df['cloud_cover'] / 10) * 
                              (1 - df['is_raining']))
        
        return df
    
    def _create_lag_features(self, df):
        """创建滞后特征"""
        print("创建滞后特征...")
        
        # 功率滞后特征
        for lag in [1, 2, 3, 6, 12, 24, 48]:
            df[f'power_lag_{lag}'] = df['power_output'].shift(lag)
        
        # 气象数据滞后特征
        weather_cols = ['solar_irradiance', 'temperature', 'humidity', 'wind_speed']
        for col in weather_cols:
            for lag in [1, 3, 6, 12]:
                df[f'{col}_lag_{lag}'] = df[col].shift(lag)
        
        return df
    
    def _create_rolling_features(self, df):
        """创建滑动窗口特征"""
        print("创建滑动窗口特征...")
        
        # 功率滑动统计特征
        for window in [3, 6, 12, 24]:
            df[f'power_mean_{window}h'] = df['power_output'].rolling(window).mean()
            df[f'power_std_{window}h'] = df['power_output'].rolling(window).std()
            df[f'power_max_{window}h'] = df['power_output'].rolling(window).max()
            df[f'power_min_{window}h'] = df['power_output'].rolling(window).min()
        
        # 气象数据滑动特征
        weather_cols = ['solar_irradiance', 'temperature', 'humidity']
        for col in weather_cols:
            for window in [6, 12, 24]:
                df[f'{col}_mean_{window}h'] = df[col].rolling(window).mean()
                df[f'{col}_std_{window}h'] = df[col].rolling(window).std()
        
        # 同期历史特征（去年同期）
        df['power_same_hour_last_week'] = df['power_output'].shift(24 * 7)
        df['power_same_hour_last_month'] = df['power_output'].shift(24 * 30)
        
        return df
    
    def _create_interaction_features(self, df):
        """创建交互特征"""
        print("创建交互特征...")
        
        # 辐照度与温度交互
        df['irradiance_temp_interaction'] = df['solar_irradiance'] * df['temperature']
        
        # 辐照度与云量交互
        df['irradiance_cloud_interaction'] = df['solar_irradiance'] * (10 - df['cloud_cover'])
        
        # 温度与湿度交互
        df['temp_humidity_interaction'] = df['temperature'] * df['humidity']
        
        # 风速与温度交互（散热效果）
        df['wind_temp_interaction'] = df['wind_speed'] * df['temperature']
        
        # 时间与辐照度交互
        df['hour_irradiance_interaction'] = df['hour'] * df['solar_irradiance']
        
        return df
    
    def _create_cyclical_features(self, df):
        """创建周期性特征"""
        print("创建周期性特征...")
        
        # 小时的周期性特征
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        
        # 月份的周期性特征
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        
        # 一年中天数的周期性特征
        df['dayofyear_sin'] = np.sin(2 * np.pi * df['dayofyear'] / 365.25)
        df['dayofyear_cos'] = np.cos(2 * np.pi * df['dayofyear'] / 365.25)
        
        # 星期的周期性特征
        df['weekday_sin'] = np.sin(2 * np.pi * df['weekday'] / 7)
        df['weekday_cos'] = np.cos(2 * np.pi * df['weekday'] / 7)
        
        return df
    
    def _handle_missing_values(self, df):
        """处理缺失值"""
        print("处理缺失值...")
        
        # 统计缺失值
        missing_counts = df.isnull().sum()
        if missing_counts.sum() > 0:
            print(f"发现 {missing_counts.sum()} 个缺失值")
            
            # 对于数值特征，使用前向填充和后向填充
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            df[numeric_cols] = df[numeric_cols].fillna(method='ffill').fillna(method='bfill')
            
            # 如果还有缺失值，使用均值填充
            remaining_missing = df[numeric_cols].isnull().sum().sum()
            if remaining_missing > 0:
                df[numeric_cols] = df[numeric_cols].fillna(df[numeric_cols].mean())
        
        return df
    
    def prepare_training_data(self, df, test_size=0.2):
        """准备训练数据"""
        print("准备训练数据...")
        
        # 移除非特征列
        exclude_cols = ['datetime', 'power_output']
        feature_cols = [col for col in df.columns if col not in exclude_cols]
        
        # 移除包含无穷大或NaN的行
        df = df.replace([np.inf, -np.inf], np.nan).dropna()
        
        X = df[feature_cols].values
        y = df[self.target_column].values
        
        # 数据标准化
        X_scaled = self.scaler.fit_transform(X)
        y_scaled = self.target_scaler.fit_transform(y.reshape(-1, 1)).ravel()
        
        # 保存特征列名
        self.feature_columns = feature_cols
        
        print(f"训练数据形状: X={X_scaled.shape}, y={y_scaled.shape}")
        
        return X_scaled, y_scaled
    
    def predict_next_24_hours(self, model, df):
        """预测未来24小时的发电功率"""
        print("生成未来24小时预测...")
        
        # 获取最后一个时间点
        last_datetime = df['datetime'].max()
        
        # 生成未来24小时的时间序列
        future_times = pd.date_range(
            start=last_datetime + timedelta(hours=1),
            periods=24,
            freq='H'
        )
        
        predictions = []
        
        for future_time in future_times:
            # 创建未来时间点的特征（这里简化处理，实际应用中需要天气预报数据）
            # 使用历史同期数据作为气象预测的近似
            same_hour_last_week = future_time - timedelta(days=7)
            historical_data = df[df['datetime'] == same_hour_last_week]
            
            if len(historical_data) > 0:
                # 使用历史同期数据
                pred_power = historical_data['power_output'].iloc[0]
            else:
                # 使用简单的周期性模型
                hour = future_time.hour
                dayofyear = future_time.dayofyear
                
                # 简化的发电功率预测
                if 6 <= hour <= 18:  # 日照时间
                    base_power = 500 * np.sin(np.pi * (hour - 6) / 12)
                    seasonal_factor = 0.8 + 0.4 * np.sin(2 * np.pi * dayofyear / 365.25)
                    pred_power = base_power * seasonal_factor
                else:
                    pred_power = 0
            
            predictions.append({
                'datetime': future_time,
                'predicted_power': max(0, pred_power)
            })
        
        predictions_df = pd.DataFrame(predictions)
        return predictions_df

if __name__ == "__main__":
    # 测试特征工程
    from data_generator import SolarDataGenerator
    
    # 生成测试数据
    generator = SolarDataGenerator()
    test_data = generator.generate_solar_data(
        start_date='2023-01-01', 
        end_date='2023-01-03',
        freq='H'
    )
    
    # 特征工程
    engineer = FeatureEngineer()
    features_data = engineer.create_features(test_data)
    
    print("\n特征工程结果:")
    print(f"原始数据形状: {test_data.shape}")
    print(f"特征数据形状: {features_data.shape}")
    print(f"新增特征数: {features_data.shape[1] - test_data.shape[1]}")
    
    # 准备训练数据
    X, y = engineer.prepare_training_data(features_data)
    print(f"训练数据形状: X={X.shape}, y={y.shape}")
