#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度学习模型
包含多种深度学习算法用于光伏发电功率预测
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import os
import warnings

warnings.filterwarnings('ignore')

# 设置随机种子
torch.manual_seed(42)
np.random.seed(42)

# PyTorch模型定义
class MLPModel(nn.Module):
    """多层感知机模型"""
    def __init__(self, input_dim, hidden_layers=[128, 64, 32], dropout_rate=0.2):
        super(MLPModel, self).__init__()
        self.layers = nn.ModuleList()

        # 输入层
        self.layers.append(nn.Linear(input_dim, hidden_layers[0]))
        self.layers.append(nn.ReLU())
        self.layers.append(nn.Dropout(dropout_rate))

        # 隐藏层
        for i in range(len(hidden_layers) - 1):
            self.layers.append(nn.Linear(hidden_layers[i], hidden_layers[i+1]))
            self.layers.append(nn.ReLU())
            self.layers.append(nn.Dropout(dropout_rate))

        # 输出层
        self.layers.append(nn.Linear(hidden_layers[-1], 1))

    def forward(self, x):
        for layer in self.layers:
            x = layer(x)
        return x

class LSTMModel(nn.Module):
    """LSTM模型"""
    def __init__(self, input_dim, hidden_dim=50, num_layers=2, dropout_rate=0.2):
        super(LSTMModel, self).__init__()
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers

        self.lstm = nn.LSTM(input_dim, hidden_dim, num_layers,
                           batch_first=True, dropout=dropout_rate if num_layers > 1 else 0)
        self.dropout = nn.Dropout(dropout_rate)
        self.fc = nn.Linear(hidden_dim, 1)

    def forward(self, x):
        # LSTM层
        lstm_out, _ = self.lstm(x)
        # 取最后一个时间步的输出
        lstm_out = lstm_out[:, -1, :]
        # Dropout
        lstm_out = self.dropout(lstm_out)
        # 全连接层
        output = self.fc(lstm_out)
        return output

class GRUModel(nn.Module):
    """GRU模型"""
    def __init__(self, input_dim, hidden_dim=50, num_layers=2, dropout_rate=0.2):
        super(GRUModel, self).__init__()
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers

        self.gru = nn.GRU(input_dim, hidden_dim, num_layers,
                         batch_first=True, dropout=dropout_rate if num_layers > 1 else 0)
        self.dropout = nn.Dropout(dropout_rate)
        self.fc = nn.Linear(hidden_dim, 1)

    def forward(self, x):
        # GRU层
        gru_out, _ = self.gru(x)
        # 取最后一个时间步的输出
        gru_out = gru_out[:, -1, :]
        # Dropout
        gru_out = self.dropout(gru_out)
        # 全连接层
        output = self.fc(gru_out)
        return output

class CNNLSTMModel(nn.Module):
    """CNN-LSTM模型"""
    def __init__(self, input_dim, cnn_filters=[64, 32], kernel_size=3,
                 lstm_hidden=50, dropout_rate=0.2):
        super(CNNLSTMModel, self).__init__()

        # CNN层
        self.conv_layers = nn.ModuleList()
        in_channels = 1
        for filters in cnn_filters:
            self.conv_layers.append(nn.Conv1d(in_channels, filters, kernel_size, padding=1))
            self.conv_layers.append(nn.ReLU())
            self.conv_layers.append(nn.Dropout(dropout_rate))
            in_channels = filters

        # LSTM层
        self.lstm = nn.LSTM(cnn_filters[-1], lstm_hidden, batch_first=True)
        self.dropout = nn.Dropout(dropout_rate)
        self.fc = nn.Linear(lstm_hidden, 1)

    def forward(self, x):
        # 调整维度用于CNN (batch, channels, sequence)
        x = x.transpose(1, 2)

        # CNN层
        for layer in self.conv_layers:
            x = layer(x)

        # 调整维度用于LSTM (batch, sequence, features)
        x = x.transpose(1, 2)

        # LSTM层
        lstm_out, _ = self.lstm(x)
        lstm_out = lstm_out[:, -1, :]
        lstm_out = self.dropout(lstm_out)

        # 输出层
        output = self.fc(lstm_out)
        return output

class DeepLearningModels:
    """深度学习模型类"""

    def __init__(self):
        """初始化深度学习模型"""
        self.models = {}
        self.trained_models = {}
        self.model_scores = {}
        self.history = {}
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")

    def create_mlp_model(self, input_dim, hidden_layers=[128, 64, 32], dropout_rate=0.2):
        """创建多层感知机模型"""
        model = MLPModel(input_dim, hidden_layers, dropout_rate)
        return model.to(self.device)

    def create_lstm_model(self, input_dim, hidden_dim=50, num_layers=2, dropout_rate=0.2):
        """创建LSTM模型"""
        model = LSTMModel(input_dim, hidden_dim, num_layers, dropout_rate)
        return model.to(self.device)

    def create_gru_model(self, input_dim, hidden_dim=50, num_layers=2, dropout_rate=0.2):
        """创建GRU模型"""
        model = GRUModel(input_dim, hidden_dim, num_layers, dropout_rate)
        return model.to(self.device)

    def create_cnn_lstm_model(self, input_dim, cnn_filters=[64, 32], kernel_size=3,
                             lstm_hidden=50, dropout_rate=0.2):
        """创建CNN-LSTM模型"""
        model = CNNLSTMModel(input_dim, cnn_filters, kernel_size, lstm_hidden, dropout_rate)
        return model.to(self.device)

    def prepare_sequence_data(self, X, y, sequence_length=24):
        """准备序列数据用于RNN模型"""
        X_seq, y_seq = [], []

        for i in range(sequence_length, len(X)):
            X_seq.append(X[i-sequence_length:i])
            y_seq.append(y[i])

        return np.array(X_seq), np.array(y_seq)

    def train_pytorch_model(self, model, X_train, y_train, X_val, y_val,
                           model_name, epochs=100, batch_size=32, lr=0.001):
        """训练PyTorch模型"""
        print(f"正在训练 {model_name}...")

        # 转换为张量
        X_train_tensor = torch.FloatTensor(X_train).to(self.device)
        y_train_tensor = torch.FloatTensor(y_train).to(self.device)
        X_val_tensor = torch.FloatTensor(X_val).to(self.device)
        y_val_tensor = torch.FloatTensor(y_val).to(self.device)

        # 创建数据加载器
        train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)

        # 定义损失函数和优化器
        criterion = nn.MSELoss()
        optimizer = optim.Adam(model.parameters(), lr=lr)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)

        # 训练历史
        train_losses = []
        val_losses = []
        best_val_loss = float('inf')
        patience_counter = 0
        patience = 20

        for epoch in range(epochs):
            # 训练模式
            model.train()
            train_loss = 0.0

            for batch_X, batch_y in train_loader:
                optimizer.zero_grad()
                outputs = model(batch_X)
                loss = criterion(outputs.squeeze(), batch_y)
                loss.backward()
                optimizer.step()
                train_loss += loss.item()

            train_loss /= len(train_loader)
            train_losses.append(train_loss)

            # 验证模式
            model.eval()
            with torch.no_grad():
                val_outputs = model(X_val_tensor)
                val_loss = criterion(val_outputs.squeeze(), y_val_tensor).item()
                val_losses.append(val_loss)

            # 学习率调度
            scheduler.step(val_loss)

            # 早停
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                # 保存最佳模型
                best_model_state = model.state_dict().copy()
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    print(f"早停在第 {epoch+1} 轮")
                    break

            if (epoch + 1) % 20 == 0:
                print(f"Epoch {epoch+1}/{epochs}, Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}")

        # 恢复最佳模型
        model.load_state_dict(best_model_state)

        # 保存训练历史
        self.history[model_name] = {
            'train_loss': train_losses,
            'val_loss': val_losses
        }

        # 评估模型
        model.eval()
        with torch.no_grad():
            y_pred = model(X_val_tensor).cpu().numpy().squeeze()
            y_val_np = y_val_tensor.cpu().numpy()

        # 计算评估指标
        mae = mean_absolute_error(y_val_np, y_pred)
        rmse = np.sqrt(mean_squared_error(y_val_np, y_pred))
        r2 = r2_score(y_val_np, y_pred)
        mape = np.mean(np.abs((y_val_np - y_pred) / (y_val_np + 1e-8))) * 100

        self.model_scores[model_name] = {
            'mae': mae,
            'rmse': rmse,
            'r2': r2,
            'mape': mape
        }

        print(f"{model_name} - MAE: {mae:.4f}, RMSE: {rmse:.4f}, R²: {r2:.4f}, MAPE: {mape:.2f}%")

        # 保存训练好的模型
        self.trained_models[model_name] = model

        return model

    def train_all_models(self, X, y, test_size=0.2, sequence_length=24):
        """训练所有深度学习模型"""
        print("开始训练所有深度学习模型...")

        # 分割数据
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=test_size, random_state=42, shuffle=False
        )

        print(f"训练集大小: {X_train.shape[0]}, 验证集大小: {X_val.shape[0]}")

        # 1. 训练MLP模型
        try:
            mlp_model = self.create_mlp_model(X_train.shape[1])
            self.train_pytorch_model(mlp_model, X_train, y_train, X_val, y_val, 'MLP')
        except Exception as e:
            print(f"训练MLP模型失败: {str(e)}")

        # 2. 准备序列数据用于RNN模型
        try:
            X_train_seq, y_train_seq = self.prepare_sequence_data(X_train, y_train, sequence_length)
            X_val_seq, y_val_seq = self.prepare_sequence_data(X_val, y_val, sequence_length)

            print(f"序列数据形状: X_train_seq={X_train_seq.shape}, X_val_seq={X_val_seq.shape}")

            # 3. 训练LSTM模型
            try:
                lstm_model = self.create_lstm_model(X_train.shape[1])
                self.train_pytorch_model(lstm_model, X_train_seq, y_train_seq,
                                       X_val_seq, y_val_seq, 'LSTM')
            except Exception as e:
                print(f"训练LSTM模型失败: {str(e)}")

            # 4. 训练GRU模型
            try:
                gru_model = self.create_gru_model(X_train.shape[1])
                self.train_pytorch_model(gru_model, X_train_seq, y_train_seq,
                                       X_val_seq, y_val_seq, 'GRU')
            except Exception as e:
                print(f"训练GRU模型失败: {str(e)}")

            # 5. 训练CNN-LSTM模型
            try:
                cnn_lstm_model = self.create_cnn_lstm_model(X_train.shape[1])
                self.train_pytorch_model(cnn_lstm_model, X_train_seq, y_train_seq,
                                       X_val_seq, y_val_seq, 'CNN_LSTM')
            except Exception as e:
                print(f"训练CNN-LSTM模型失败: {str(e)}")

        except Exception as e:
            print(f"准备序列数据失败: {str(e)}")

        # 保存模型
        self.save_models()

        return self.trained_models

    def predict(self, model_name, X):
        """使用指定模型进行预测"""
        if model_name not in self.trained_models:
            raise ValueError(f"模型 {model_name} 尚未训练")

        model = self.trained_models[model_name]
        model.eval()

        with torch.no_grad():
            X_tensor = torch.FloatTensor(X).to(self.device)
            predictions = model(X_tensor).cpu().numpy()

        return predictions.squeeze()

    def save_models(self, save_dir='results/models'):
        """保存所有训练好的模型"""
        os.makedirs(save_dir, exist_ok=True)

        for model_name, model in self.trained_models.items():
            model_file = os.path.join(save_dir, f'{model_name}_pytorch.pth')
            torch.save({
                'model_state_dict': model.state_dict(),
                'model_class': model.__class__.__name__,
                'model_config': getattr(model, 'config', {})
            }, model_file)

        print(f"PyTorch模型已保存到 {save_dir}")

    def load_models(self, save_dir='results/models'):
        """加载保存的模型"""
        if not os.path.exists(save_dir):
            print(f"模型目录 {save_dir} 不存在")
            return

        model_files = [f for f in os.listdir(save_dir) if f.endswith('_pytorch.pth')]

        for model_file in model_files:
            model_name = model_file.replace('_pytorch.pth', '')
            model_path = os.path.join(save_dir, model_file)

            try:
                checkpoint = torch.load(model_path, map_location=self.device)
                # 这里需要根据保存的模型类型重新创建模型
                # 简化处理，实际使用时需要保存模型配置
                print(f"发现PyTorch模型文件: {model_name}")
            except Exception as e:
                print(f"加载PyTorch模型 {model_name} 失败: {str(e)}")

        print(f"从 {save_dir} 检查了 {len(model_files)} 个PyTorch模型文件")

    def get_best_model(self):
        """获取最佳深度学习模型"""
        if not self.model_scores:
            print("没有模型评估分数")
            return None

        # 基于R²选择最佳模型
        best_model_name = max(self.model_scores.keys(),
                             key=lambda x: self.model_scores[x]['r2'])

        return best_model_name, self.trained_models[best_model_name]

if __name__ == "__main__":
    # 测试深度学习模型
    from sklearn.datasets import make_regression

    # 生成测试数据
    X, y = make_regression(n_samples=1000, n_features=20, noise=0.1, random_state=42)

    # 创建深度学习模型实例
    dl_models = DeepLearningModels()

    # 训练所有模型
    trained_models = dl_models.train_all_models(X, y)

    print(f"\n训练完成，共训练了 {len(trained_models)} 个深度学习模型")

    # 获取最佳模型
    if dl_models.model_scores:
        best_model_name, best_model = dl_models.get_best_model()
        print(f"最佳深度学习模型: {best_model_name}")
    else:
        print("没有成功训练的模型")