#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度学习模型
包含多种深度学习算法用于光伏发电功率预测
"""

import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers, models, callbacks
from tensorflow.keras.optimizers import Adam, RMSprop
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import os
import warnings

# 设置TensorFlow日志级别
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
warnings.filterwarnings('ignore')

class DeepLearningModels:
    """深度学习模型类"""
    
    def __init__(self):
        """初始化深度学习模型"""
        self.models = {}
        self.trained_models = {}
        self.model_scores = {}
        self.history = {}
        
        # 设置随机种子
        tf.random.set_seed(42)
        
    def create_mlp_model(self, input_dim, hidden_layers=[128, 64, 32], dropout_rate=0.2):
        """创建多层感知机模型"""
        model = models.Sequential()
        
        # 输入层
        model.add(layers.Dense(hidden_layers[0], activation='relu', input_dim=input_dim))
        model.add(layers.Dropout(dropout_rate))
        
        # 隐藏层
        for units in hidden_layers[1:]:
            model.add(layers.Dense(units, activation='relu'))
            model.add(layers.Dropout(dropout_rate))
        
        # 输出层
        model.add(layers.Dense(1, activation='linear'))
        
        return model
    
    def create_lstm_model(self, sequence_length, n_features, lstm_units=[50, 50], dropout_rate=0.2):
        """创建LSTM模型"""
        model = models.Sequential()
        
        # 第一个LSTM层
        model.add(layers.LSTM(
            lstm_units[0], 
            return_sequences=len(lstm_units) > 1,
            input_shape=(sequence_length, n_features)
        ))
        model.add(layers.Dropout(dropout_rate))
        
        # 额外的LSTM层
        for i, units in enumerate(lstm_units[1:], 1):
            return_seq = i < len(lstm_units) - 1
            model.add(layers.LSTM(units, return_sequences=return_seq))
            model.add(layers.Dropout(dropout_rate))
        
        # 全连接层
        model.add(layers.Dense(50, activation='relu'))
        model.add(layers.Dropout(dropout_rate))
        
        # 输出层
        model.add(layers.Dense(1, activation='linear'))
        
        return model
    
    def create_gru_model(self, sequence_length, n_features, gru_units=[50, 50], dropout_rate=0.2):
        """创建GRU模型"""
        model = models.Sequential()
        
        # 第一个GRU层
        model.add(layers.GRU(
            gru_units[0], 
            return_sequences=len(gru_units) > 1,
            input_shape=(sequence_length, n_features)
        ))
        model.add(layers.Dropout(dropout_rate))
        
        # 额外的GRU层
        for i, units in enumerate(gru_units[1:], 1):
            return_seq = i < len(gru_units) - 1
            model.add(layers.GRU(units, return_sequences=return_seq))
            model.add(layers.Dropout(dropout_rate))
        
        # 全连接层
        model.add(layers.Dense(50, activation='relu'))
        model.add(layers.Dropout(dropout_rate))
        
        # 输出层
        model.add(layers.Dense(1, activation='linear'))
        
        return model
    
    def create_cnn_lstm_model(self, sequence_length, n_features, 
                             cnn_filters=[64, 32], kernel_size=3, 
                             lstm_units=50, dropout_rate=0.2):
        """创建CNN-LSTM模型"""
        model = models.Sequential()
        
        # CNN层
        model.add(layers.Conv1D(
            filters=cnn_filters[0], 
            kernel_size=kernel_size, 
            activation='relu',
            input_shape=(sequence_length, n_features)
        ))
        model.add(layers.Dropout(dropout_rate))
        
        for filters in cnn_filters[1:]:
            model.add(layers.Conv1D(filters=filters, kernel_size=kernel_size, activation='relu'))
            model.add(layers.Dropout(dropout_rate))
        
        # LSTM层
        model.add(layers.LSTM(lstm_units, dropout=dropout_rate))
        
        # 全连接层
        model.add(layers.Dense(50, activation='relu'))
        model.add(layers.Dropout(dropout_rate))
        
        # 输出层
        model.add(layers.Dense(1, activation='linear'))
        
        return model
    
    def create_attention_model(self, sequence_length, n_features, 
                              lstm_units=50, attention_units=32, dropout_rate=0.2):
        """创建带注意力机制的模型"""
        # 输入层
        inputs = layers.Input(shape=(sequence_length, n_features))
        
        # LSTM层
        lstm_out = layers.LSTM(lstm_units, return_sequences=True)(inputs)
        lstm_out = layers.Dropout(dropout_rate)(lstm_out)
        
        # 注意力机制
        attention = layers.Dense(attention_units, activation='tanh')(lstm_out)
        attention = layers.Dense(1, activation='softmax')(attention)
        attention = layers.Flatten()(attention)
        attention = layers.RepeatVector(lstm_units)(attention)
        attention = layers.Permute([2, 1])(attention)
        
        # 应用注意力权重
        weighted = layers.Multiply()([lstm_out, attention])
        weighted = layers.Lambda(lambda x: tf.reduce_sum(x, axis=1))(weighted)
        
        # 全连接层
        dense = layers.Dense(50, activation='relu')(weighted)
        dense = layers.Dropout(dropout_rate)(dense)
        
        # 输出层
        outputs = layers.Dense(1, activation='linear')(dense)
        
        model = models.Model(inputs=inputs, outputs=outputs)
        
        return model
    
    def prepare_sequence_data(self, X, y, sequence_length=24):
        """准备序列数据用于RNN模型"""
        X_seq, y_seq = [], []
        
        for i in range(sequence_length, len(X)):
            X_seq.append(X[i-sequence_length:i])
            y_seq.append(y[i])
        
        return np.array(X_seq), np.array(y_seq)
    
    def train_single_model(self, model_name, model, X_train, y_train, 
                          X_val, y_val, epochs=100, batch_size=32):
        """训练单个深度学习模型"""
        print(f"正在训练 {model_name}...")
        
        # 编译模型
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='mse',
            metrics=['mae']
        )
        
        # 回调函数
        callbacks_list = [
            callbacks.EarlyStopping(
                monitor='val_loss',
                patience=20,
                restore_best_weights=True
            ),
            callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=10,
                min_lr=1e-7
            )
        ]
        
        # 训练模型
        history = model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=epochs,
            batch_size=batch_size,
            callbacks=callbacks_list,
            verbose=0
        )
        
        # 保存训练历史
        self.history[model_name] = history.history
        
        # 评估模型
        y_pred = model.predict(X_val, verbose=0)
        
        # 计算评估指标
        mae = mean_absolute_error(y_val, y_pred)
        rmse = np.sqrt(mean_squared_error(y_val, y_pred))
        r2 = r2_score(y_val, y_pred)
        mape = np.mean(np.abs((y_val - y_pred.flatten()) / (y_val + 1e-8))) * 100
        
        self.model_scores[model_name] = {
            'mae': mae,
            'rmse': rmse,
            'r2': r2,
            'mape': mape
        }
        
        print(f"{model_name} - MAE: {mae:.4f}, RMSE: {rmse:.4f}, R²: {r2:.4f}, MAPE: {mape:.2f}%")
        
        # 保存训练好的模型
        self.trained_models[model_name] = model
        
        return model
    
    def train_all_models(self, X, y, test_size=0.2, sequence_length=24):
        """训练所有深度学习模型"""
        print("开始训练所有深度学习模型...")
        
        # 分割数据
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=test_size, random_state=42, shuffle=False
        )
        
        print(f"训练集大小: {X_train.shape[0]}, 验证集大小: {X_val.shape[0]}")
        
        # 1. 训练MLP模型
        try:
            mlp_model = self.create_mlp_model(X_train.shape[1])
            self.train_single_model('MLP', mlp_model, X_train, y_train, X_val, y_val)
        except Exception as e:
            print(f"训练MLP模型失败: {str(e)}")
        
        # 2. 准备序列数据用于RNN模型
        try:
            X_train_seq, y_train_seq = self.prepare_sequence_data(X_train, y_train, sequence_length)
            X_val_seq, y_val_seq = self.prepare_sequence_data(X_val, y_val, sequence_length)
            
            print(f"序列数据形状: X_train_seq={X_train_seq.shape}, X_val_seq={X_val_seq.shape}")
            
            # 3. 训练LSTM模型
            try:
                lstm_model = self.create_lstm_model(sequence_length, X_train.shape[1])
                self.train_single_model('LSTM', lstm_model, X_train_seq, y_train_seq, 
                                      X_val_seq, y_val_seq)
            except Exception as e:
                print(f"训练LSTM模型失败: {str(e)}")
            
            # 4. 训练GRU模型
            try:
                gru_model = self.create_gru_model(sequence_length, X_train.shape[1])
                self.train_single_model('GRU', gru_model, X_train_seq, y_train_seq, 
                                      X_val_seq, y_val_seq)
            except Exception as e:
                print(f"训练GRU模型失败: {str(e)}")
            
            # 5. 训练CNN-LSTM模型
            try:
                cnn_lstm_model = self.create_cnn_lstm_model(sequence_length, X_train.shape[1])
                self.train_single_model('CNN_LSTM', cnn_lstm_model, X_train_seq, y_train_seq, 
                                      X_val_seq, y_val_seq)
            except Exception as e:
                print(f"训练CNN-LSTM模型失败: {str(e)}")
            
            # 6. 训练注意力模型
            try:
                attention_model = self.create_attention_model(sequence_length, X_train.shape[1])
                self.train_single_model('Attention', attention_model, X_train_seq, y_train_seq, 
                                      X_val_seq, y_val_seq)
            except Exception as e:
                print(f"训练注意力模型失败: {str(e)}")
                
        except Exception as e:
            print(f"准备序列数据失败: {str(e)}")
        
        # 保存模型
        self.save_models()
        
        return self.trained_models
    
    def predict(self, model_name, X):
        """使用指定模型进行预测"""
        if model_name not in self.trained_models:
            raise ValueError(f"模型 {model_name} 尚未训练")
        
        model = self.trained_models[model_name]
        return model.predict(X, verbose=0)
    
    def save_models(self, save_dir='results/models'):
        """保存所有训练好的模型"""
        os.makedirs(save_dir, exist_ok=True)
        
        for model_name, model in self.trained_models.items():
            model_file = os.path.join(save_dir, f'{model_name}_dl.h5')
            model.save(model_file)
        
        print(f"深度学习模型已保存到 {save_dir}")
    
    def load_models(self, save_dir='results/models'):
        """加载保存的模型"""
        if not os.path.exists(save_dir):
            print(f"模型目录 {save_dir} 不存在")
            return
        
        model_files = [f for f in os.listdir(save_dir) if f.endswith('_dl.h5')]
        
        for model_file in model_files:
            model_name = model_file.replace('_dl.h5', '')
            model_path = os.path.join(save_dir, model_file)
            
            try:
                self.trained_models[model_name] = keras.models.load_model(model_path)
                print(f"已加载深度学习模型: {model_name}")
            except Exception as e:
                print(f"加载模型 {model_name} 失败: {str(e)}")
        
        print(f"从 {save_dir} 加载了 {len(self.trained_models)} 个深度学习模型")
    
    def get_best_model(self):
        """获取最佳深度学习模型"""
        if not self.model_scores:
            print("没有模型评估分数")
            return None
        
        # 基于R²选择最佳模型
        best_model_name = max(self.model_scores.keys(), 
                             key=lambda x: self.model_scores[x]['r2'])
        
        return best_model_name, self.trained_models[best_model_name]

if __name__ == "__main__":
    # 测试深度学习模型
    from sklearn.datasets import make_regression
    
    # 生成测试数据
    X, y = make_regression(n_samples=1000, n_features=20, noise=0.1, random_state=42)
    
    # 创建深度学习模型实例
    dl_models = DeepLearningModels()
    
    # 训练所有模型
    trained_models = dl_models.train_all_models(X, y)
    
    print(f"\n训练完成，共训练了 {len(trained_models)} 个深度学习模型")
    
    # 获取最佳模型
    if dl_models.model_scores:
        best_model_name, best_model = dl_models.get_best_model()
        print(f"最佳深度学习模型: {best_model_name}")
    else:
        print("没有成功训练的模型")
