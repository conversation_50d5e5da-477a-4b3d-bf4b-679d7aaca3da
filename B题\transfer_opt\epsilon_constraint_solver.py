#!/usr/bin/env python
# coding: utf-8
"""
城市垃圾分类运输的中转站选址与路径优化 - ε-constraint求解方法
通过约束碳排放上限，求解成本最小化问题，生成Pareto前沿曲线
使用PuLP开源优化库
"""

import time
import numpy as np
import matplotlib.pyplot as plt
import pulp as pl  # 使用PuLP替代Gurobi
from data_processor import load_data, prepare_optimization_data
import os
import matplotlib.font_manager as fm

# 设置中文字体
def set_chinese_font():
    try:
        chinese_fonts = [f.name for f in fm.fontManager.ttflist 
                         if 'simhei' in f.name.lower() or 'microsoft yahei' in f.name.lower() 
                         or 'simsun' in f.name.lower() or 'fangsong' in f.name.lower() 
                         or 'kaiti' in f.name.lower()]
        if chinese_fonts:
            plt.rcParams['font.family'] = chinese_fonts[0]
            return True
    except:
        pass
    print("设置中文字体失败，将使用英文标题")
    return False

def solve_with_epsilon_constraint(data, epsilon):
    """
    使用ε-constraint方法求解优化问题
    
    Args:
        data: 优化数据
        epsilon: 碳排放上限约束
        
    Returns:
        优化结果字典
    """
    print(f"\n===== 求解ε-constraint优化问题 (碳排放上限: {epsilon:.2f}吨CO2) =====")
    
    # 提取数据
    collection_ids = data['collection_ids']  # 收集点ID: 1-30
    transfer_ids = data['transfer_ids']      # 中转站ID: 31-35
    distance_matrix = data['distance_matrix']
    waste_types = data['waste_types']
    demands_by_type = data['demands_by_type']
    station_capacities = data['station_capacities']
    station_costs = data['station_costs']
    vehicle_costs = data['vehicle_costs']
    carbon_emissions = data['carbon_emissions']
    
    # 创建PuLP优化模型
    model = pl.LpProblem("Transfer_Station_Epsilon_Constraint", pl.LpMinimize)
    
    # 决策变量：是否建设中转站j (0-1变量)
    y = {}
    for j in transfer_ids:
        y[j] = pl.LpVariable(f"y_{j}", cat=pl.LpBinary)
    
    # 决策变量：收集点i是否分配给中转站j (0-1变量)
    x = {}
    for i in collection_ids:
        for j in transfer_ids:
            x[(i, j)] = pl.LpVariable(f"x_{i}_{j}", cat=pl.LpBinary)
    
    # 目标函数1：最小化总成本(运输成本 + 建设成本)
    # 运输成本 = 从收集点到中转站的距离 * 单位成本 * 分配关系
    transport_cost = pl.lpSum([
        2 * distance_matrix[i][j] * vehicle_costs[waste_type] * x[(i, j)]
        for waste_type in waste_types
        for i in collection_ids
        for j in transfer_ids
    ])
    
    # 建设成本 = 中转站的固定建设费用 * 是否建设
    construction_cost = pl.lpSum([station_costs[j-31] * y[j] for j in transfer_ids])
    
    # 设置成本最小化为目标函数
    model += transport_cost + construction_cost
    
    # 计算目标函数2：碳排放量
    # 碳排放 = 基础排放(距离相关) + 载重排放(垃圾量相关)
    carbon_expr = pl.lpSum([
        (2 * distance_matrix[i][j] * carbon_emissions[waste_type]['alpha'] +  # 基础排放
         2 * distance_matrix[i][j] * carbon_emissions[waste_type]['beta'] * demands_by_type[waste_type][i])  # 载重排放
        * x[(i, j)]
        for waste_type in waste_types
        for i in collection_ids
        for j in transfer_ids
    ])
    
    # 添加碳排放约束
    # 将碳排放量从kg转换为吨
    model += carbon_expr / 1000 <= epsilon, "carbon_emission_constraint"
    
    # 约束条件1：每个收集点必须分配给一个中转站
    for i in collection_ids:
        model += pl.lpSum([x[(i, j)] for j in transfer_ids]) == 1, f"unique_assignment_{i}"
    
    # 约束条件2：只能分配给已建设的中转站
    for i in collection_ids:
        for j in transfer_ids:
            model += x[(i, j)] <= y[j], f"assign_to_built_{i}_{j}"
    
    # 约束条件3：中转站容量约束
    for j in transfer_ids:
        for waste_type in waste_types:
            # 所有分配到该中转站的垃圾量不超过其容量
            waste_sum = pl.lpSum([
                demands_by_type[waste_type][i] * x[(i, j)] for i in collection_ids
            ])
            model += waste_sum <= station_capacities[j][waste_type] * y[j], f"capacity_{j}_{waste_type}"
    
    # 约束条件4：最多建设5个中转站
    model += pl.lpSum([y[j] for j in transfer_ids]) <= 5, "max_stations"
    
    # 求解模型
    start_time = time.time()
    solver = pl.PULP_CBC_CMD(timeLimit=300, msg=True, gapRel=0.01)  # 设置时间限制和相对差距
    model.solve(solver)
    solve_time = time.time() - start_time
    
    # 输出求解结果
    if pl.LpStatus[model.status] == 'Optimal' or pl.LpStatus[model.status] == 'Not Solved':
        print(f"优化完成! 状态: {pl.LpStatus[model.status]}, 用时: {solve_time:.2f}秒")
        print(f"目标值(总成本): {pl.value(model.objective):.2f}元")
        
        # 计算实际碳排放量
        actual_carbon = pl.value(carbon_expr) / 1000  # 转换为吨
        print(f"碳排放量: {actual_carbon:.2f}吨CO2")
        
        # 收集结果
        selected_stations = []
        allocations = {}
        
        for j in transfer_ids:
            if pl.value(y[j]) > 0.5:  # 如果变量值接近1，则视为选择该站点
                selected_stations.append(j)
                allocations[j] = []
        
        for i in collection_ids:
            for j in transfer_ids:
                if pl.value(x[(i, j)]) > 0.5:  # 如果变量值接近1，则视为分配关系成立
                    allocations[j].append(i)
        
        # 计算各中转站的垃圾量
        station_loads = {}
        for j in selected_stations:
            station_loads[j] = {}
            for waste_type in waste_types:
                station_loads[j][waste_type] = sum(demands_by_type[waste_type][i] 
                                                for i in allocations[j])
        
        return {
            'status': 'optimal',
            'objective': pl.value(model.objective),
            'transport_cost': pl.value(transport_cost),
            'construction_cost': pl.value(construction_cost),
            'carbon_emission': actual_carbon,
            'selected_stations': selected_stations,
            'allocations': allocations,
            'station_loads': station_loads,
            'solve_time': solve_time,
            'epsilon': epsilon
        }
    else:
        print(f"优化失败! 状态: {pl.LpStatus[model.status]}")
        if pl.LpStatus[model.status] == 'Infeasible':
            print(f"问题不可行，碳排放上限 {epsilon:.2f}吨CO2 太低")
        
        return {
            'status': 'failed',
            'model_status': pl.LpStatus[model.status],
            'epsilon': epsilon
        }

def generate_pareto_front(data):
    """
    生成Pareto前沿曲线
    
    Args:
        data: 优化数据
        
    Returns:
        Pareto前沿点列表
    """
    print("正在生成Pareto前沿曲线...")
    
    # 找到极限值
    # 先不考虑碳排放约束，求解成本最小化问题
    # 使用一个非常大的数值替代无限值
    cost_min_result = solve_with_epsilon_constraint(data, 10000.0)
    if cost_min_result['status'] != 'optimal':
        print("无法确定成本最小值，生成Pareto前沿失败")
        return []
    
    # 成本最小解对应的碳排放量
    e_worst = cost_min_result['carbon_emission']
    print(f"成本最小解的碳排放量: {e_worst:.2f}吨CO2")
    
    # 尝试找到最小可行碳排放量
    # 二分查找方法
    e_min = 0
    e_max = e_worst
    e_best = e_max
    tolerance = 0.01  # 容差
    
    while e_max - e_min > tolerance:
        e_mid = (e_min + e_max) / 2
        print(f"尝试碳排放上限: {e_mid:.2f}吨CO2")
        result = solve_with_epsilon_constraint(data, e_mid)
        
        if result['status'] == 'optimal':
            e_best = min(e_best, result['carbon_emission'])
            e_max = e_mid
        else:
            e_min = e_mid
    
    print(f"碳排放最小值约为: {e_best:.2f}吨CO2")
    
    # 在最小和最大碳排放之间，均匀取点
    num_points = 6  # 生成6个Pareto点
    epsilon_values = np.linspace(e_best, e_worst, num_points)
    
    pareto_points = []
    
    # 对每个epsilon值求解优化问题
    for i, eps in enumerate(epsilon_values):
        print(f"\n===== 求解Pareto点 {i+1}/{num_points} =====")
        result = solve_with_epsilon_constraint(data, eps)
        
        if result['status'] == 'optimal':
            pareto_points.append(result)
            print(f"Pareto点 {i+1}: 成本={result['objective']:.2f}元, 碳排放={result['carbon_emission']:.2f}吨CO2")
    
    return pareto_points

def plot_pareto_front(pareto_points):
    """绘制Pareto前沿曲线"""
    if not pareto_points:
        print("没有有效的Pareto点，无法绘制曲线")
        return
    
    # 提取成本和碳排放数据
    costs = [p['objective'] for p in pareto_points]
    emissions = [p['carbon_emission'] for p in pareto_points]
    
    # 创建Pareto前沿曲线图
    plt.figure(figsize=(10, 6))
    set_chinese_font()
    
    plt.plot(emissions, costs, 'o-', color='blue', linewidth=2, markersize=8)
    
    # 在每个点上标注序号
    for i, (emission, cost) in enumerate(zip(emissions, costs)):
        plt.annotate(f"{i+1}", (emission, cost), xytext=(5, 5), 
                     textcoords='offset points', fontsize=10)
    
    # 添加标题和标签
    plt.title('成本与碳排放的Pareto前沿曲线', fontsize=16)
    plt.xlabel('碳排放量 (吨CO2)', fontsize=12)
    plt.ylabel('总成本 (元)', fontsize=12)
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 标注每个点的具体值
    for i, (emission, cost) in enumerate(zip(emissions, costs)):
        plt.annotate(f"成本: {cost:.2f}元\n排放: {emission:.2f}吨CO2", 
                    (emission, cost), xytext=(10, -20 if i % 2 == 0 else 20), 
                    textcoords='offset points', fontsize=8,
                    bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8))
    
    # 保存图片
    os.makedirs('results', exist_ok=True)
    plt.savefig('results/pareto_front.png', dpi=300, bbox_inches='tight')
    plt.savefig('results/pareto_front.pdf', bbox_inches='tight')
    plt.close()
    
    print("Pareto前沿曲线已保存到results/pareto_front.png和results/pareto_front.pdf")

def plot_station_selection(data, pareto_points):
    """比较不同Pareto点的中转站选择情况"""
    if not pareto_points:
        print("没有有效的Pareto点，无法绘制中转站选择图")
        return
    
    # 提取所有可能的中转站
    transfer_ids = data['transfer_ids']
    
    # 创建选择矩阵，行是Pareto点，列是中转站
    selection_matrix = np.zeros((len(pareto_points), len(transfer_ids)))
    
    for i, point in enumerate(pareto_points):
        for j, station_id in enumerate(transfer_ids):
            if station_id in point['selected_stations']:
                selection_matrix[i, j] = 1
    
    # 创建热图
    plt.figure(figsize=(12, 6))
    set_chinese_font()
    
    # 创建自定义颜色映射
    cmap = plt.cm.colors.ListedColormap(['white', 'green'])
    
    plt.imshow(selection_matrix, cmap=cmap, aspect='auto')
    
    # 添加网格线
    plt.grid(False)
    for i in range(selection_matrix.shape[0] + 1):
        plt.axhline(i - 0.5, color='black', linewidth=1)
    for j in range(selection_matrix.shape[1] + 1):
        plt.axvline(j - 0.5, color='black', linewidth=1)
    
    # 添加标签
    plt.yticks(range(len(pareto_points)), [f"方案{i+1}\n成本:{p['objective']:.0f}元\n排放:{p['carbon_emission']:.2f}吨" 
                                          for i, p in enumerate(pareto_points)])
    plt.xticks(range(len(transfer_ids)), [f"站点{j}" for j in transfer_ids])
    
    # 在每个单元格中添加选择状态
    for i in range(selection_matrix.shape[0]):
        for j in range(selection_matrix.shape[1]):
            if selection_matrix[i, j] == 1:
                plt.text(j, i, "√", ha='center', va='center', fontsize=12)
    
    # 添加标题
    plt.title('不同方案的中转站选择情况', fontsize=16)
    plt.xlabel('候选中转站编号', fontsize=12)
    
    # 保存图片
    plt.savefig('results/station_selection.png', dpi=300, bbox_inches='tight')
    plt.savefig('results/station_selection.pdf', bbox_inches='tight')
    plt.close()
    
    print("中转站选择比较图已保存到results/station_selection.png和results/station_selection.pdf")

def plot_solution_details(data, pareto_point, point_id):
    """为单个Pareto点绘制详细解决方案图"""
    # 提取数据
    locations = data['locations']
    depot_id = data['depot_id']
    collection_ids = data['collection_ids']
    transfer_ids = data['transfer_ids']
    selected_stations = pareto_point['selected_stations']
    allocations = pareto_point['allocations']
    
    # 创建图形
    plt.figure(figsize=(12, 10))
    set_chinese_font()
    
    # 绘制处理厂
    plt.scatter(locations[depot_id][0], locations[depot_id][1], 
                c='red', s=200, marker='*', label='处理厂')
    
    # 绘制收集点
    plt.scatter([locations[i][0] for i in collection_ids], 
                [locations[i][1] for i in collection_ids],
                c='black', s=50, label='收集点')
    
    # 绘制中转站(区分选中和未选中)
    unselected = [j for j in transfer_ids if j not in selected_stations]
    if unselected:
        plt.scatter([locations[j][0] for j in unselected], 
                    [locations[j][1] for j in unselected],
                    c='gray', s=100, marker='s', label='未选中转站')
    
    if selected_stations:
        plt.scatter([locations[j][0] for j in selected_stations], 
                    [locations[j][1] for j in selected_stations],
                    c='blue', s=150, marker='s', label='已选中转站')
    
    # 为每个点添加编号
    for i in range(len(locations)):
        plt.annotate(str(i), (locations[i][0], locations[i][1]), 
                     xytext=(5, 5), textcoords='offset points', fontsize=10)
    
    # 使用不同颜色绘制不同中转站的分配
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']
    
    for idx, j in enumerate(selected_stations):
        color = colors[idx % len(colors)]
        # 绘制从收集点到中转站的连线
        for i in allocations[j]:
            plt.plot([locations[i][0], locations[j][0]], 
                     [locations[i][1], locations[j][1]], 
                     c=color, linestyle='-', alpha=0.5)
    
    # 添加标题和标签
    plt.title(f'方案{point_id}的中转站选址与收集点分配\n成本: {pareto_point["objective"]:.0f}元, 碳排放: {pareto_point["carbon_emission"]:.2f}吨CO2', fontsize=16)
    plt.xlabel('X坐标 (km)', fontsize=12)
    plt.ylabel('Y坐标 (km)', fontsize=12)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend(loc='best')
    
    # 保存图片
    plt.savefig(f'results/solution_{point_id}.png', dpi=300, bbox_inches='tight')
    plt.savefig(f'results/solution_{point_id}.pdf', bbox_inches='tight')
    plt.close()
    
    print(f"方案{point_id}的详细解决方案图已保存到results/solution_{point_id}.png和results/solution_{point_id}.pdf")

def plot_cost_breakdown(pareto_points):
    """比较不同Pareto点的成本构成"""
    if not pareto_points:
        print("没有有效的Pareto点，无法绘制成本构成图")
        return
    
    # 提取数据
    transport_costs = [p['transport_cost'] for p in pareto_points]
    construction_costs = [p['construction_cost'] for p in pareto_points]
    
    # 创建堆叠柱状图
    plt.figure(figsize=(12, 6))
    set_chinese_font()
    
    x = range(len(pareto_points))
    width = 0.6
    
    # 绘制堆叠柱状图
    p1 = plt.bar(x, transport_costs, width, label='运输成本')
    p2 = plt.bar(x, construction_costs, width, bottom=transport_costs, label='建设成本')
    
    # 添加标签和标题
    plt.title('不同方案的成本构成', fontsize=16)
    plt.xlabel('优化方案', fontsize=12)
    plt.ylabel('成本 (元)', fontsize=12)
    plt.xticks(x, [f"方案{i+1}" for i in range(len(pareto_points))])
    plt.legend(loc='upper center', bbox_to_anchor=(0.5, -0.10), ncol=2)
    
    # 在每个柱子上标注具体值
    for i in range(len(pareto_points)):
        total = transport_costs[i] + construction_costs[i]
        trans_ratio = transport_costs[i] / total * 100
        const_ratio = construction_costs[i] / total * 100
        
        plt.text(i, transport_costs[i] / 2, f"{trans_ratio:.1f}%", 
                ha='center', va='center', color='white', fontweight='bold')
        plt.text(i, transport_costs[i] + construction_costs[i] / 2, f"{const_ratio:.1f}%", 
                ha='center', va='center', color='white', fontweight='bold')
        plt.text(i, total + 20000, f"{total:.0f}元", 
                ha='center', va='bottom', color='black')
    
    # 保存图片
    plt.tight_layout()
    plt.savefig('results/cost_breakdown.png', dpi=300, bbox_inches='tight')
    plt.savefig('results/cost_breakdown.pdf', bbox_inches='tight')
    plt.close()
    
    print("成本构成比较图已保存到results/cost_breakdown.png和results/cost_breakdown.pdf")

def save_summary_table(pareto_points):
    """保存Pareto点的摘要表"""
    if not pareto_points:
        print("没有有效的Pareto点，无法生成摘要表")
        return
    
    # 创建结果目录
    os.makedirs('results', exist_ok=True)
    
    # 打开文件写入摘要
    with open('results/pareto_summary.txt', 'w', encoding='utf-8') as f:
        f.write("ε-constraint方法优化结果摘要\n")
        f.write("================================\n\n")
        
        f.write("Pareto前沿点列表:\n")
        f.write("方案ID   总成本(元)   运输成本(元)   建设成本(元)   碳排放(吨CO2)   已选中转站\n")
        f.write("-" * 80 + "\n")
        
        for i, point in enumerate(pareto_points):
            f.write(f"{i+1:^8}{point['objective']:^14.2f}{point['transport_cost']:^15.2f}"
                  f"{point['construction_cost']:^15.2f}{point['carbon_emission']:^16.2f}"
                  f"{', '.join(map(str, point['selected_stations'])):^12}\n")
        
        f.write("\n\n")
        
        # 详细方案信息
        for i, point in enumerate(pareto_points):
            f.write(f"方案{i+1}详细信息:\n")
            f.write(f"总成本: {point['objective']:.2f}元\n")
            f.write(f"运输成本: {point['transport_cost']:.2f}元\n")
            f.write(f"建设成本: {point['construction_cost']:.2f}元\n")
            f.write(f"碳排放: {point['carbon_emission']:.2f}吨CO2\n")
            f.write(f"求解时间: {point['solve_time']:.2f}秒\n")
            f.write(f"已选中转站: {', '.join(map(str, point['selected_stations']))}\n\n")
            
            f.write("收集点分配情况:\n")
            for j in point['selected_stations']:
                f.write(f"  中转站{j}: {point['allocations'][j]}\n")
            
            f.write("\n垃圾量分配情况:\n")
            for j in point['selected_stations']:
                f.write(f"  中转站{j}:\n")
                for waste_type, amount in point['station_loads'][j].items():
                    f.write(f"    {waste_type}: {amount:.2f}吨\n")
            f.write("\n" + "-" * 40 + "\n\n")
    
    print("Pareto前沿摘要表已保存到results/pareto_summary.txt")

def main():
    """主函数"""
    print("开始使用ε-constraint方法求解城市垃圾分类运输中转站选址与路径优化问题...")
    
    # 创建结果目录
    os.makedirs('results', exist_ok=True)
    
    # 加载和准备数据
    raw_data = load_data()
    opt_data = prepare_optimization_data(raw_data)
    
    # 使用ε-constraint方法生成Pareto前沿曲线
    pareto_points = generate_pareto_front(opt_data)
    
    if pareto_points:
        # 绘制Pareto前沿曲线
        plot_pareto_front(pareto_points)
        
        # 绘制每个Pareto点的详细解决方案
        for i, point in enumerate(pareto_points):
            plot_solution_details(opt_data, point, i+1)
        
        # 比较不同方案的中转站选择情况
        plot_station_selection(opt_data, pareto_points)
        
        # 比较不同方案的成本构成
        plot_cost_breakdown(pareto_points)
        
        # 保存摘要表
        save_summary_table(pareto_points)
        
        print("\n优化完成! 所有结果已保存到results目录")
    else:
        print("优化失败，无法生成Pareto前沿曲线")

if __name__ == "__main__":
    main() 