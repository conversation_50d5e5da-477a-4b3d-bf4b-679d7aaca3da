#!/usr/bin/env python
# coding: utf-8
"""
CVRP求解器 - 第一问解决方案
直接从JSON文件读取数据，并输出结果到文件
增加路径可视化功能，计算平均载重率
"""

import math
import time
import json
import sys
import numpy as np
import matplotlib.pyplot as plt
from ortools.constraint_solver import routing_enums_pb2
from ortools.constraint_solver import pywrapcp

# 重定向输出到文件
output_file = open('result.txt', 'w', encoding='utf-8')
def print_and_log(message):
    """同时打印到控制台和日志文件"""
    print(message)
    output_file.write(message + '\n')
    output_file.flush()  # 实时写入文件

def create_data_model():
    """准备数据模型 - 从JSON文件读取数据，只使用collection_points"""
    data = {}
    
    print_and_log("正在从data.json文件读取数据...")
    try:
        with open('data.json', 'r', encoding='utf-8') as f:
            json_data = json.load(f)
        print_and_log("JSON数据读取成功")
    except Exception as e:
        print_and_log(f"读取JSON数据失败: {e}")
        sys.exit(1)
    
    # 处理厂坐标 (0,0)
    locations = [(0, 0)]  # 处理厂
    demands = [0]         # 处理厂需求为0
    
    # 读取收集点数据 - 仅使用collection_points
    collection_points = json_data['collection_points']
    print_and_log(f"JSON中的收集点数量: {len(collection_points)}个")
    
    # 按id排序收集点
    sorted_points = sorted(collection_points, key=lambda p: p['id'])
    
    for point in sorted_points:
        locations.append((point['x'], point['y']))
        # 使用total_waste作为垃圾量
        demands.append(point['total_waste'])
    
    print_and_log("使用收集点的total_waste作为垃圾量数据")
    
    # 设置数据模型
    data['locations'] = locations
    data['demands'] = demands
    data['vehicle_capacity'] = 5  # 车辆载重5吨
    
    # 移除车辆数量限制，设置一个足够大的值
    total_demand = sum(demands)
    min_vehicles_needed = math.ceil(total_demand / data['vehicle_capacity'])
    data['num_vehicles'] = min_vehicles_needed * 2  # 设置为理论最小值的两倍，确保足够
    
    data['depot'] = 0             # 处理厂编号为0
    
    print_and_log(f"数据读取完成! 共{len(locations)}个点, 包括处理厂")
    print_and_log(f"总垃圾量: {sum(demands):.2f}吨")
    print_and_log(f"理论最小车辆数: {min_vehicles_needed}辆 (不考虑路径优化)")
    
    return data

def compute_euclidean_distance_matrix(locations):
    """计算欧氏距离矩阵"""
    distances = {}
    for from_node in range(len(locations)):
        distances[from_node] = {}
        for to_node in range(len(locations)):
            if from_node == to_node:
                distances[from_node][to_node] = 0
            else:
                # 计算欧氏距离
                distances[from_node][to_node] = (
                    math.sqrt(
                        (locations[from_node][0] - locations[to_node][0]) ** 2 +
                        (locations[from_node][1] - locations[to_node][1]) ** 2))
    return distances

def print_solution(data, manager, routing, solution):
    """输出求解结果并返回路径数据用于绘图"""
    print_and_log(f'目标值: {solution.ObjectiveValue()}')
    total_distance = 0
    total_load = 0
    
    # 统计使用车辆数 (实际路径数)
    used_vehicles = 0
    
    # 收集路径数据用于绘图
    routes_for_plot = []
    loads = []
    distances = []
    
    for vehicle_id in range(data['num_vehicles']):
        index = routing.Start(vehicle_id)
        # 检查是否使用了该车辆
        if solution.Value(routing.NextVar(index)) == routing.End(vehicle_id):
            continue  # 该车辆未使用
            
        used_vehicles += 1
        plan_output = f'路径 {used_vehicles}:'
        route_distance = 0
        route_load = 0
        
        route_for_plot = []
        
        while not routing.IsEnd(index):
            node_index = manager.IndexToNode(index)
            route_load += data['demands'][node_index]
            plan_output += f' {node_index} ->'
            route_for_plot.append(node_index)
            
            previous_index = index
            index = solution.Value(routing.NextVar(index))
            route_distance += routing.GetArcCostForVehicle(
                previous_index, index, vehicle_id)
                
        node_index = manager.IndexToNode(index)
        route_for_plot.append(node_index)  # 添加返回处理厂的路径
        
        plan_output += f' {node_index}'
        plan_output += f' (距离: {route_distance:.2f}, 载重: {route_load:.2f}吨)'
        print_and_log(plan_output)
        total_distance += route_distance
        total_load += route_load
        
        # 保存绘图数据
        routes_for_plot.append(route_for_plot)
        loads.append(route_load)
        distances.append(route_distance)
    
    # 计算各种评价指标
    # 1. 平均载重率
    avg_load_ratio = (total_load / (used_vehicles * data['vehicle_capacity'])) * 100
    
    # 2. 平均每车行驶距离
    avg_distance_per_vehicle = total_distance / used_vehicles if used_vehicles > 0 else 0
    
    # 3. 平均每车载重
    avg_load_per_vehicle = total_load / used_vehicles if used_vehicles > 0 else 0
    
    # 4. 最长路径和最短路径
    max_distance = max(distances) if distances else 0
    min_distance = min(distances) if distances else 0
    max_load = max(loads) if loads else 0
    min_load = min(loads) if loads else 0
    
    # 5. 负载均衡性 (载重标准差)
    load_std = np.std(loads) if len(loads) > 1 else 0
    distance_std = np.std(distances) if len(distances) > 1 else 0
    
    # 6. 每吨垃圾的行驶距离 (效率指标)
    distance_per_ton = total_distance / total_load if total_load > 0 else 0
    
    # 7. 访问点数统计
    total_points = sum(len(route)-2 for route in routes_for_plot)  # 每条路径减去起点和终点
    avg_points_per_vehicle = total_points / used_vehicles if used_vehicles > 0 else 0
    
    # 输出基本统计数据
    print_and_log('\n====== 路径优化结果统计 ======')
    print_and_log(f'总距离: {total_distance:.2f} km')
    print_and_log(f'总载重: {total_load:.2f} 吨')
    print_and_log(f'使用车辆数: {used_vehicles}')
    
    # 输出评价指标
    print_and_log('\n====== 评价指标 ======')
    print_and_log(f'1. 平均载重率: {avg_load_ratio:.2f}%')
    print_and_log(f'2. 平均每车行驶距离: {avg_distance_per_vehicle:.2f} km')
    print_and_log(f'3. 平均每车载重: {avg_load_per_vehicle:.2f} 吨')
    print_and_log(f'4. 每吨垃圾的行驶距离: {distance_per_ton:.2f} km/吨')
    print_and_log(f'5. 平均每车访问点数: {avg_points_per_vehicle:.2f} 个')
    
    # 输出分布情况
    print_and_log('\n====== 分布情况 ======')
    print_and_log(f'路径距离: 最长={max_distance:.2f} km, 最短={min_distance:.2f} km, 标准差={distance_std:.2f}')
    print_and_log(f'载重分布: 最大={max_load:.2f} 吨, 最小={min_load:.2f} 吨, 标准差={load_std:.2f}')
    
    # 各车辆的载重率
    print_and_log("\n各车辆载重率:")
    for i, load in enumerate(loads):
        load_ratio = (load / data['vehicle_capacity']) * 100
        print_and_log(f'车辆 {i+1}: {load_ratio:.2f}% ({load:.2f}/{data["vehicle_capacity"]:.2f}吨)')
    
    return routes_for_plot, total_distance, used_vehicles, avg_load_ratio, avg_distance_per_vehicle, distance_per_ton

def plot_routes(data, routes):
    """绘制路径图"""
    plt.figure(figsize=(12, 10))
    
    # 设置中文字体支持
    try:
        # 尝试设置中文字体
        import matplotlib.font_manager as fm
        # 检查系统中文字体
        chinese_fonts = [f.name for f in fm.fontManager.ttflist if 'simhei' in f.name.lower() or 'microsoft yahei' in f.name.lower() or 'simsun' in f.name.lower() or 'fangsong' in f.name.lower() or 'kaiti' in f.name.lower()]
        if chinese_fonts:
            plt.rcParams['font.family'] = chinese_fonts[0]
        else:
            # 如果没有找到中文字体，则使用英文标题
            print_and_log("未找到支持中文的字体，将使用英文标题")
    except:
        print_and_log("设置中文字体失败，将使用英文标题")
    
    # 绘制所有点
    locations = data['locations']
    depot_x, depot_y = locations[0]
    
    # 绘制收集点
    plt.scatter([loc[0] for loc in locations[1:]], 
                [loc[1] for loc in locations[1:]],
                c='blue', s=50, label='Collection Points')
    
    # 突出显示处理厂
    plt.scatter(depot_x, depot_y, c='red', s=200, marker='*', label='Depot (0)')
    
    # 为每个点添加编号
    for i, loc in enumerate(locations):
        plt.annotate(str(i), (loc[0], loc[1]), xytext=(5, 5), 
                    textcoords='offset points', fontsize=10)
    
    # 使用不同颜色绘制每条路径
    colors = plt.cm.rainbow(np.linspace(0, 1, len(routes)))
    
    for i, route in enumerate(routes):
        route_x = [locations[j][0] for j in route]
        route_y = [locations[j][1] for j in route]
        plt.plot(route_x, route_y, 'o-', c=colors[i], linewidth=2, 
                label=f'Route {i+1}')
    
    # 添加标题和标签 - 使用英文避免字体问题
    plt.title('CVRP Optimized Routes (Vehicle Capacity: 5 tons, Total Waste)', fontsize=15)
    plt.xlabel('X Coordinate (km)', fontsize=12)
    plt.ylabel('Y Coordinate (km)', fontsize=12)
    plt.grid(True)
    
    # 添加图例，但控制显示数量避免过多
    if len(routes) > 8:
        plt.legend(loc='upper center', bbox_to_anchor=(0.5, -0.05),
                  fancybox=True, shadow=True, ncol=5, fontsize=10)
    else:
        plt.legend(loc='best', fontsize=10)
    
    # 保存图片
    plt.savefig('cvrp_routes.png', dpi=300, bbox_inches='tight')
    plt.savefig('cvrp_routes.pdf', bbox_inches='tight')  # 保存为PDF用于论文
    plt.close()
    
    print_and_log("路径图已保存为 'cvrp_routes.png' 和 'cvrp_routes.pdf'")

def main():
    """问题1求解主函数"""
    try:
        # 准备数据
        data = create_data_model()
        
        # 创建路由索引管理器
        manager = pywrapcp.RoutingIndexManager(len(data['locations']),
                                            data['num_vehicles'], data['depot'])
        
        # 创建路由模型
        routing = pywrapcp.RoutingModel(manager)
        
        # 创建并注册距离矩阵回调函数
        distance_matrix = compute_euclidean_distance_matrix(data['locations'])
        
        def distance_callback(from_index, to_index):
            from_node = manager.IndexToNode(from_index)
            to_node = manager.IndexToNode(to_index)
            return distance_matrix[from_node][to_node]
        
        transit_callback_index = routing.RegisterTransitCallback(distance_callback)
        
        # 定义距离成本
        routing.SetArcCostEvaluatorOfAllVehicles(transit_callback_index)
        
        # 添加容量约束
        def demand_callback(from_index):
            from_node = manager.IndexToNode(from_index)
            return data['demands'][from_node] * 100  # 将吨转为100公斤整数单位
        
        demand_callback_index = routing.RegisterUnaryTransitCallback(
            demand_callback)
        routing.AddDimensionWithVehicleCapacity(
            demand_callback_index,
            0,  # null capacity slack
            [data['vehicle_capacity'] * 100] * data['num_vehicles'],  # 车辆容量
            True,  # 从起始位置开始计算
            'Capacity')
        
        # 设置求解策略
        search_parameters = pywrapcp.DefaultRoutingSearchParameters()
        search_parameters.first_solution_strategy = (
            routing_enums_pb2.FirstSolutionStrategy.PATH_CHEAPEST_ARC)
        search_parameters.local_search_metaheuristic = (
            routing_enums_pb2.LocalSearchMetaheuristic.GUIDED_LOCAL_SEARCH)
        search_parameters.time_limit.seconds = 120  # 增加求解时间到2分钟
        
        # 求解问题
        print_and_log("开始求解...")
        start_time = time.time()
        solution = routing.SolveWithParameters(search_parameters)
        solve_time = time.time() - start_time
        
        # 输出结果
        if solution:
            print_and_log(f"求解成功! 用时: {solve_time:.2f}秒")
            results = print_solution(data, manager, routing, solution)
            routes, total_distance, used_vehicles, avg_load_ratio, avg_distance_per_vehicle, distance_per_ton = results
            
            # 绘制路径图
            plot_routes(data, routes)
            
            # 保存简要结果到单独文件
            with open('summary.txt', 'w', encoding='utf-8') as f:
                f.write(f"CVRP问题1求解摘要\n")
                f.write(f"===================\n")
                f.write(f"优化时间: {solve_time:.2f} 秒\n")
                f.write(f"总行驶距离: {total_distance:.2f} km\n")
                f.write(f"使用车辆数: {used_vehicles}\n")
                f.write(f"平均载重率: {avg_load_ratio:.2f}%\n")
                f.write(f"平均每车行驶距离: {avg_distance_per_vehicle:.2f} km\n")
                f.write(f"每吨垃圾的行驶距离: {distance_per_ton:.2f} km/吨\n")
                f.write(f"车辆载重上限: {data['vehicle_capacity']} 吨\n")
                f.write(f"总垃圾量: {sum(data['demands']):.2f} 吨\n")
        else:
            print_and_log('没有找到解!')
            print_and_log(f"求解耗时: {time.time() - start_time:.2f}秒")
    
    except Exception as e:
        print_and_log(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc(file=output_file)
    
    finally:
        # 关闭输出文件
        output_file.close()
        print("结果已保存到result.txt文件中")

if __name__ == '__main__':
    main() 