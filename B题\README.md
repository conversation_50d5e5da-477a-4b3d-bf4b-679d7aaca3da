# 城市垃圾分类运输的路径优化与调度

## 项目简介

本项目解决"2025年电工杯竞赛"B题"城市垃圾分类运输的路径优化与调度"问题。主要功能包括：

1. 将Excel数据转换为JSON格式以便后续处理
2. 实现问题1：单一垃圾类型、单一车辆类型的最优路径规划

## 文件说明

- `problem1.py` - 实现问题1的主要代码
- `data.json` - 转换后的数据集
- `result_厨余垃圾.json` - 问题1的求解结果

## 运行环境要求

- Python 3.7+
- 依赖包：
  - pandas
  - numpy
  - ortools
  - openpyxl (用于读取Excel文件)

## 安装依赖

```bash
pip install pandas numpy ortools openpyxl
```

## 运行方法

直接运行问题1的代码：

```bash
python problem1.py
```

## 问题说明

### 问题1

在单一垃圾类型、单一车辆类型、可多次往返的前提下，最小化每天车辆行驶总距离。需要确定：

1. 需要多少辆车（或说"多少趟"）
2. 每辆车（每趟）装多少垃圾、按什么顺序访问哪些收集点

所有垃圾都被运回处理厂（编号0）后任务完成。30个收集点的坐标与日产量见附件1，车辆额定载重Q=5t。

## 实现方法

使用Google的OR-Tools的CP-SAT求解器实现容量受限的车辆路径问题(CVRP)，采用以下步骤：

1. 读取Excel数据并转换为JSON格式
2. 构建距离矩阵
3. 设定决策变量和约束条件
4. 求解模型并输出结果

## 数据格式

Excel数据被转换为JSON格式，便于后续处理：
- `collection_points`: 收集点信息（id, x, y, total_waste）
- `vehicles`: 车辆信息（id, waste_type, capacity, volume, cost_per_km等）
- `waste_distribution`: 各收集点的四类垃圾量
- `transfer_stations`: 中转站信息 